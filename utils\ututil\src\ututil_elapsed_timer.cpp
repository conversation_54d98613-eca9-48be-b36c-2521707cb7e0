#include "ututil_elapsed_timer.h"

USE_UTUTIL;

CElapsedTimer::CElapsedTimer()
{
    reset();
}

/**
 * @brief 计时器重置
 */
void CElapsedTimer::reset() 
{ 
    m_begin = std::chrono::high_resolution_clock::now(); 
}

/**
 * @brief 计算从计时器开始到当前之间的时间差（时）
 * @return 返回时间差
 */
INT64 CElapsedTimer::elapsedHour() const
{
    return std::chrono::duration_cast<std::chrono::hours>(std::chrono::high_resolution_clock::now() - m_begin).count();
}

/**
 * @brief 计算从计时器开始到当前之间的时间差（分）
 * @return 返回时间差
 */
INT64 CElapsedTimer::elapsedMin() const
{
    return std::chrono::duration_cast<std::chrono::minutes>(std::chrono::high_resolution_clock::now() - m_begin).count();
}

/**
 * @brief 计算从计时器开始到当前之间的时间差（秒）
 * @return 返回时间差
 */
INT64 CElapsedTimer::elapsedSec() const
{
    return std::chrono::duration_cast<std::chrono::seconds>(std::chrono::high_resolution_clock::now() - m_begin).count();
}

/**
 * @brief 计算从计时器开始到当前之间的时间差（毫秒）
 * @return 返回时间差
 */
INT64 CElapsedTimer::elapsedMsec() const
{
    return std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::high_resolution_clock::now() - m_begin).count();
}

/**
 * @brief 计算从计时器开始到当前之间的时间差（微秒）
 * @return 返回时间差
 */
INT64 CElapsedTimer::elapsedMicro() const
{
    return std::chrono::duration_cast<std::chrono::microseconds>(std::chrono::high_resolution_clock::now() - m_begin).count();
}
