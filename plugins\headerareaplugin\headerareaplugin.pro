TARGET = headerareaplugin
TEMPLATE = lib
CONFIG += plugin

QT += core gui
greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

# 输出目录
DESTDIR = $$(UTSTENV)/shlib/plugins

# 对象文件目录
OBJECTS_DIR = $$(UTSTENV)/object/plugins/headerareaplugin
MOC_DIR = $$(UTSTENV)/moc/plugins/headerareaplugin

# 包含路径
INCLUDEPATH += $$(UTSTENV)/interfaces \
               $$(UTSTENV)/core \
               $$(UTSTENV)/utils/utlog/include

# 依赖库
LIBS += -L$$(UTSTENV)/shlib \
        -lcore \
        -lutlog

# 运行时库路径
QMAKE_RPATHDIR += $$(UTSTENV)/shlib

# 源文件
SOURCES += \
    headerareaplugin.cpp \
    headerareawidget.cpp

# 头文件
HEADERS += \
    headerareaplugin.h \
    headerareawidget.h


# 在MSVC中处理C++11标准支持
win32-msvc* {
    # MSVC不支持std=c++11选项，使用默认设置
} else {
    QMAKE_CXXFLAGS += -std=c++11
}