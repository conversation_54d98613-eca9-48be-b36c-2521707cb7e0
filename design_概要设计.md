# 智能运载小车控制端界面设计文档

## 概述

本设计文档描述了基于Qt框架开发的智能运载小车控制端界面的技术架构和实现方案。系统采用插件化设计，支持实时视频监控、设备状态监测、远程控制等核心功能，确保操作的安全性和可靠性。

## 系统架构

### 整体架构

系统采用插件化架构，包含以下主要组件：

#### 系统架构图

下图展示了整体的插件化架构，包括主应用程序、插件管理器、各个插件模块之间的关系和依赖：

**架构层次说明：**
- **主应用程序层**：包含主窗口、插件管理器和数据管理器，负责整个系统的协调和管理
- **插件接口层**：定义了插件的标准接口和通用类型，确保插件的一致性
- **功能插件层**：各个具体的功能插件，包括顶部区域、视频显示、距离信息、控制区域和调试设置
- **工具类库层**：提供日志记录等基础服务
- **数据层**：各种数据模型，负责数据的存储和管理
- **数据采集层**：统一的数据源接入层，支持多种数据源类型的采集和管理

```mermaid
graph TB
    subgraph "主应用程序层"
        MW[MainWindow<br/>主窗口]
        PM[PluginManager<br/>插件管理器]
        DM[DataManager<br/>数据管理器]
    end
    
    subgraph "插件接口层"
        IP[IPlugin<br/>插件接口]
        CT[CommonTypes<br/>通用类型]
    end
    
    subgraph "功能插件层"
        HAP[HeaderAreaPlugin<br/>顶部区域插件]
        VDP[VideoDisplayPlugin<br/>视频显示插件]
        DIP[DistanceInfoPlugin<br/>距离信息插件]
        CAP[ControlAreaPlugin<br/>控制区域插件]
        DSP[DebugSettingsPlugin<br/>调试设置插件]
        BLP[BusinessLogicPlugin<br/>业务逻辑插件]
    end
    
    subgraph "工具类库层"
        UTLOG[UTLog<br/>日志记录库]
        UTUTIL[UTUtil<br/>通用工具库]
    end
    
    subgraph "数据层"
        VD[VideoData<br/>视频数据]
        SD[StatusData<br/>状态数据]
        DD[DistanceData<br/>距离数据]
        CD[ControlData<br/>控制数据]
        DSD[DebugSettingsData<br/>调试设置数据]
        BLD[BusinessLogicData<br/>业务逻辑数据]
    end

    subgraph "数据采集层"
        DCM[DataCollectionManager<br/>数据采集管理器]
        IDA[IDataAdapter<br/>数据适配器接口]
        SA[SensorAdapter<br/>传感器适配器]
        NA[NetworkAdapter<br/>网络适配器]
        CA[CameraAdapter<br/>摄像头适配器]
        FA[FileAdapter<br/>文件适配器]
    end

    subgraph "业务逻辑层"
        BLE[BusinessLogicEngine<br/>业务逻辑执行引擎]
        IBC[IBusinessCommand<br/>业务命令接口]
        AAC[AutoAlignmentCommand<br/>自动对齐命令]
        PPC[PathPlanningCommand<br/>路径规划命令]
        DCC[DeviceCalibrationCommand<br/>设备校准命令]
        SCC[SystemCheckCommand<br/>系统检查命令]
    end
    
    %% 主要依赖关系
    MW --> PM
    MW --> DM
    PM --> IP
    
    %% 插件依赖
    HAP -.-> IP
    VDP -.-> IP
    DIP -.-> IP
    CAP -.-> IP
    DSP -.-> IP
    BLP -.-> IP
    
    %% 数据管理关系
    DM --> VD
    DM --> SD
    DM --> DD
    DM --> CD
    DM --> DSD
    DM --> BLD
    DM --> DCM

    %% 数据采集层关系
    DCM --> IDA
    SA -.-> IDA
    NA -.-> IDA
    CA -.-> IDA
    FA -.-> IDA
    DCM --> VD
    DCM --> SD
    DCM --> DD

    %% 插件与数据的关系
    VDP --> VD
    HAP --> SD
    DIP --> DD
    CAP --> CD
    DSP --> DSD
    BLP --> BLD

    %% 业务逻辑层关系
    BLP --> BLE
    BLE --> IBC
    AAC -.-> IBC
    PPC -.-> IBC
    DCC -.-> IBC
    SCC -.-> IBC
    BLE --> AAC
    BLE --> PPC
    BLE --> DCC
    BLE --> SCC
    
    %% 工具类使用关系
    HAP --> UTLOG
    VDP --> UTLOG
    DSP --> UTLOG
    BLP --> UTLOG
    BLE --> UTLOG

    %% 工具库依赖关系
    UTLOG --> UTUTIL
    
    %% 样式定义
    classDef mainApp fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef plugin fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef interface fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef utils fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef data fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef dataCollection fill:#fff8e1,stroke:#f57f17,stroke-width:2px
    classDef businessLogic fill:#e3f2fd,stroke:#1565c0,stroke-width:2px

    class MW,PM,DM mainApp
    class HAP,VDP,DIP,CAP,DSP,BLP plugin
    class IP,CT,IDA interface
    class UTLOG,UTUTIL utils
    class VD,SD,DD,CD,DSD,BLD data
    class DCM,SA,NA,CA,FA dataCollection
    class BLE,IBC,AAC,PPC,DCC,SCC businessLogic
```

**关系说明：**
- 实线箭头表示直接依赖关系
- 虚线箭头表示接口实现关系
- 每个插件都实现IPlugin接口，通过PluginManager进行管理
- 所有数据通过DataManager进行统一管理和分发

### 技术栈

- **界面框架**: Qt 4.8.6 + Qt Widgets
- **视频处理**: Qt Multimedia (Qt 4.8.6) + OpenCV 2.x
- **通信协议**: QTcpSocket/QUdpSocket + QSerialPort
- **数据格式**: JSON (QJson) / 自定义协议
- **多线程**: QThread + Qt信号槽机制
- **插件系统**: QPluginLoader + Qt插件接口
- **构建系统**: qmake (.pro文件)
- **目标平台**: Ubuntu Linux x86_64
- **部署方式**: Qt插件(.so文件) + 主程序
- **环境变量**: 使用 `$$(UTSTENV)` 作为软件根目录引用

### 编码规范

#### 日志记录统一要求

**强制要求**：项目中的所有代码必须统一使用utlog库进行日志记录，禁止使用其他日志记录方式。

**日志记录规范**：
1. **统一日志库**：所有模块必须使用utlog库，包括：
   - 主程序（main）
   - 核心库（core）
   - 所有插件（plugins）
   - 数据采集模块（datacollection）
   - 业务逻辑模块（businesslogicplugin）

2. **日志级别使用**：
   ```cpp
   UTLOG_TRACE(message)    // 详细跟踪信息，仅在调试时使用
   UTLOG_DEBUG(message)    // 调试信息，开发和测试阶段使用
   UTLOG_INFO(message)     // 一般信息，正常运行状态记录
   UTLOG_WARN(message)     // 警告信息，潜在问题提醒
   UTLOG_ERROR(message)    // 错误信息，功能异常但程序可继续
   UTLOG_CRITICAL(message) // 严重错误，系统可能无法继续运行
   ```

3. **日志初始化**：
   ```cpp
   // 在main函数或插件初始化中设置
   UTLOG_SET_LOGPATH("./logs");           // 设置日志目录
   UTLOG_SET_LEVEL(utlog::log_level::info); // 设置日志级别
   UTLOG_SET_CONSOLE_ENABLE(true);        // 启用控制台输出（调试时）
   UTLOG_SET_TRACE_ENABLE(true);          // 启用位置跟踪（调试时）
   ```

4. **禁止使用的日志方式**：
   - 禁止使用 `printf`、`cout`、`qDebug` 等进行日志输出
   - 禁止使用其他第三方日志库
   - 禁止直接写入文件进行日志记录

5. **日志内容规范**：
   - 使用有意义的日志消息，包含足够的上下文信息
   - 关键操作必须记录日志（初始化、错误、状态变更等）
   - 敏感信息不得记录到日志中

#### 文件和目录命名规范

**基本原则**：
- 所有文件名必须使用小写字母
- 所有目录名必须使用小写字母
- 单词之间不使用分隔符，采用连续小写字母
- 文件扩展名保持标准格式（.h、.cpp、.pro等）

**文件命名示例**：
```
正确示例：
- headerareaplugin.h
- headerareaplugin.cpp
- videodisplayplugin.h
- distanceinfoplugin.cpp
- debugsettingsdialog.h

错误示例：
- HeaderAreaPlugin.h
- VideoDisplayPlugin.cpp
- DebugSettingsDialog.h
- header_area_plugin.h
- header-area-plugin.cpp
```

**目录命名示例**：
```
正确示例：
- headerareaplugin/
- videodisplayplugin/
- distanceinfoplugin/
- businesslogicplugin/
- datacollection/

错误示例：
- HeaderAreaPlugin/
- VideoDisplayPlugin/
- DistanceInfoPlugin/
- BusinessLogicPlugin/
- DataCollection/
```

#### 类名命名规范

**基本原则**：
- 类名保持Pascal命名法（首字母大写的驼峰命名）
- 类名不受文件名规范影响
- 接口类以"I"开头
- 抽象类以"Abstract"开头

**类名示例**：
```cpp
// 正确的类名（不受文件名影响）
class HeaderAreaPlugin;
class VideoDisplayWidget;
class DebugSettingsDialog;
class IPlugin;
class AbstractBusinessCommand;

// 对应的文件名（小写）
// headerareaplugin.h/cpp
// videodisplaywidget.h/cpp
// debugsettingsdialog.h/cpp
// iplugin.h
// abstractbusinesscommand.h/cpp
```

#### 项目文件命名规范

**qmake项目文件(.pro)**：
- 使用小写命名
- 与目录名保持一致

**示例**：
```
目录名：headerareaplugin/
项目文件：headerareaplugin.pro
主类名：HeaderAreaPlugin
```

#### 命名一致性原则

1. **文件与类的对应关系**：
   ```
   文件名：headerareaplugin.h/cpp
   类名：HeaderAreaPlugin
   目录名：headerareaplugin/
   项目文件：headerareaplugin.pro
   ```

2. **插件命名模式**：
   ```
   插件类：XxxPlugin
   插件文件：xxxplugin.h/cpp
   插件目录：xxxplugin/
   插件项目：xxxplugin.pro
   ```

3. **组件命名模式**：
   ```
   组件类：XxxWidget/XxxDialog
   组件文件：xxxwidget.h/cpp 或 xxxdialog.h/cpp
   ```

### 环境变量配置

#### UTSTENV环境变量

**基本概念**：
- `UTSTENV` 是项目的根目录环境变量
- 所有qmake项目文件(.pro)都使用此环境变量进行路径引用
- 确保项目在不同开发环境中的可移植性

**环境变量设置**：
```bash
# 在 ~/.bashrc 或 ~/.profile 中添加
export UTSTENV=/path/to/your/project/root

# 例如：
export UTSTENV=/home/<USER>/UtSmartTrolley
```

**输出目录结构**：
```
$UTSTENV/
├── exec/                          # 可执行程序输出目录
│   └── utsmarttrolley             # 主程序可执行文件
├── shlib/                         # 库文件输出目录
│   ├── libinterfaces.so           # 接口库
│   ├── libutils.so                # 工具库
│   ├── headerareaplugin.so        # 顶部区域插件
│   ├── videodisplayplugin.so      # 视频显示插件
│   ├── distanceinfoplugin.so      # 距离信息插件
│   ├── controlareaplugin.so       # 控制区域插件
│   ├── debugsettingsplugin.so     # 调试设置插件
│   └── businesslogicplugin.so     # 业务逻辑插件
└── [源代码目录结构...]
```

**qmake项目文件中的使用**：
```pro
# 主程序输出到exec目录
DESTDIR = $$(UTSTENV)/exec

# 插件输出到shlib目录
DESTDIR = $$(UTSTENV)/shlib

# 库依赖路径引用
LIBS += -L$$(UTSTENV)/shlib -linterfaces -lutils

# 包含路径引用
INCLUDEPATH += $$(UTSTENV)/interfaces \
               $$(UTSTENV)/utils
```

### 项目结构

```
$UTSTENV/                          # 项目根目录（环境变量）
├── exec/                          # 可执行程序输出目录
│   └── utsmarttrolley             # 主程序可执行文件
├── shlib/                         # 库文件和插件输出目录
│   ├── libinterfaces.so           # 接口库
│   ├── libutils.so                # 工具库
│   └── [各插件.so文件]            # 所有插件库文件
├── main/                          # 主程序源码
│   ├── main.pro                   # 输出到 $$(UTSTENV)/exec/
│   ├── main.cpp
│   └── mainwindow.h/cpp           # MainWindow类
├── interfaces/                    # 纯接口定义（无.pro文件）
│   ├── iplugin.h                  # 插件接口定义
│   ├── ibusinesscommand.h         # 业务命令接口
│   ├── idataadapter.h             # 数据适配器接口
│   └── commontypes.h              # 通用类型定义
├── core/                          # 核心组件库
│   ├── core.pro                   # 输出到 $$(UTSTENV)/shlib/
│   ├── datamanager.h/cpp          # DataManager类实现
│   └── pluginmanager.h/cpp        # PluginManager类实现
├── datacollection/                # 数据采集库
│   ├── datacollection.pro         # 输出到 $$(UTSTENV)/shlib/
│   ├── abstractdataadapter.h/cpp  # AbstractDataAdapter类
│   ├── datacollectionmanager.h/cpp # DataCollectionManager类
│   ├── sensoradapter.h/cpp        # SensorAdapter类实现
│   └── networkapiadapter.h/cpp    # NetworkAPIAdapter类实现
├── plugins/                       # 插件源码目录
│   ├── headerareaplugin/          # HeaderAreaPlugin类
│   │   ├── headerareaplugin.pro   # 输出到 $$(UTSTENV)/shlib/
│   │   ├── headerareaplugin.h/cpp
│   │   └── headerareawidget.h/cpp # HeaderAreaWidget类
│   ├── videodisplayplugin/        # VideoDisplayPlugin类
│   │   ├── videodisplayplugin.pro # 输出到 $$(UTSTENV)/shlib/
│   │   ├── videodisplayplugin.h/cpp
│   │   └── videodisplaywidget.h/cpp # VideoDisplayWidget类
│   ├── distanceinfoplugin/        # DistanceInfoPlugin类
│   │   ├── distanceinfoplugin.pro # 输出到 $$(UTSTENV)/shlib/
│   │   ├── distanceinfoplugin.h/cpp
│   │   └── distanceinfowidget.h/cpp # DistanceInfoWidget类
│   ├── controlareaplugin/         # ControlAreaPlugin类
│   │   ├── controlareaplugin.pro  # 输出到 $$(UTSTENV)/shlib/
│   │   ├── controlareaplugin.h/cpp
│   │   └── controlareawidget.h/cpp # ControlAreaWidget类
│   ├── debugsettingsplugin/       # DebugSettingsPlugin类
│   │   ├── debugsettingsplugin.pro # 输出到 $$(UTSTENV)/shlib/
│   │   ├── debugsettingsplugin.h/cpp
│   │   ├── debugsettingswidget.h/cpp # DebugSettingsWidget类
│   │   └── debugsettingsdialog.h/cpp # DebugSettingsDialog类
│   └── businesslogicplugin/       # BusinessLogicPlugin类（包含完整业务逻辑实现）
│       ├── businesslogicplugin.pro # 输出到 $$(UTSTENV)/shlib/
│       ├── businesslogicplugin.h/cpp # 插件主类
│       ├── businesslogicwidget.h/cpp # BusinessLogicWidget类
│       ├── businesslogicsettingswidget.h/cpp # BusinessLogicSettingsWidget类
│       ├── engine/                # 业务逻辑引擎实现
│       │   ├── businesslogicengine.h/cpp # BusinessLogicEngine类
│       │   ├── businesslogicdata.h/cpp # BusinessLogicData类
│       │   └── abstractbusinesscommand.h/cpp # AbstractBusinessCommand类
│       └── commands/              # 具体业务命令实现
│           ├── autoalignmentcommand.h/cpp # AutoAlignmentCommand类
│           ├── pathplanningcommand.h/cpp # PathPlanningCommand类
│           ├── devicecalibrationcommand.h/cpp # DeviceCalibrationCommand类
│           └── systemcheckcommand.h/cpp # SystemCheckCommand类
├── utils/                         # 通用工具类库源码
│   ├── utils.pro                  # 主构建文件（子项目管理，utlog依赖ututil）
│   ├── utlog/                     # 日志记录库
│   │   ├── utlog.pro              # 输出到 $$(UTSTENV)/shlib/
│   │   ├── README.md              # utlog库使用文档
│   │   ├── include/
│   │   │   └── utlog.h            # 日志库主要头文件
│   │   ├── src/
│   │   │   ├── utlog.cpp          # 日志库实现
│   │   │   └── utlogger.h         # 内部日志器实现
│   │   └── third/
│   │       └── spdlog/            # spdlog第三方库
│   └── ututil/                    # 通用工具库
│       ├── ututil.pro             # 输出到 $$(UTSTENV)/shlib/
│       ├── README.md              # ututil库使用文档
│       ├── include/               # 头文件目录（26个工具模块）
│       │   ├── ututil_tools.h     # 基础工具和平台定义
│       │   ├── ututil_string.h    # 字符串处理工具
│       │   ├── ututil_file.h      # 文件操作工具
│       │   ├── ututil_thread.h    # 线程管理
│       │   ├── ututil_mutex.h     # 互斥锁
│       │   ├── ututil_time.h      # 时间工具
│       │   ├── ututil_json.h      # JSON处理
│       │   ├── ututil_net.h       # 网络工具
│       │   ├── ututil_md5.h       # MD5加密
│       │   ├── ututil_hex.h       # HEX编码
│       │   ├── ututil_ini.h       # INI配置文件
│       │   ├── ututil_random.h    # 随机数生成
│       │   ├── ututil_safelist.h  # 线程安全列表
│       │   ├── ututil_safemap.h   # 线程安全映射
│       │   ├── ututil_safeset.h   # 线程安全集合
│       │   ├── ututil_sharedptr.h # 智能指针
│       │   ├── ututil_autolock.h  # 自动锁
│       │   ├── ututil_task.h      # 任务管理
│       │   ├── ututil_watchdog.h  # 看门狗
│       │   └── [其他工具模块...]  # 更多工具类头文件
│       ├── src/                   # 源文件目录
│       └── third/                 # 第三方库
│           ├── iniparser/         # INI解析库
│           ├── jsoncpp/           # JSON处理库
│           ├── randutils/         # 随机数工具
│           └── simpleini/         # 简单INI库
└── utsmarttrolley.pro            # 主构建文件（输出到 $$(UTSTENV)/exec/）
```

## 核心接口设计

### 架构理念：接口与实现分离

#### 设计原则

**接口定义纯净化**：
- `interfaces/` 目录只包含纯接口定义头文件
- 不包含任何具体实现代码（.cpp文件）
- 不需要编译，通过INCLUDEPATH被其他模块引用
- 保持接口的稳定性和向后兼容性

**实现模块化**：
- 具体实现按功能域组织为独立的库和插件
- `core/` - 核心组件库（DataManager、PluginManager）
- `datacollection/` - 数据采集库（数据适配器、采集管理器）
- `utils/` - 工具库（日志记录等通用功能）
- `businesslogicplugin/` - 业务逻辑插件（包含完整的业务逻辑实现）

**插件自包含设计**：
- 业务逻辑相关的所有实现都包含在businesslogicplugin中
- 避免创建不必要的中间库层次
- 符合插件化架构的自包含原则
- 简化了依赖关系和构建配置

**依赖关系清晰**：
- 所有模块都依赖interfaces目录的接口定义
- 库之间通过明确的依赖关系建立层次结构
- 插件通过链接核心库获得功能支持

### 插件接口

#### 设计理念

IPlugin接口遵循"简单即美"的设计理念，只保留核心必要的功能方法：

- **简化元数据**：移除版本号、作者信息等不必要的元数据字段
- **核心功能**：专注于插件的基本生命周期管理和UI创建
- **可选扩展**：提供设置界面的可选支持
- **易于实现**：减少插件开发的复杂度，提高开发效率

#### 接口定义

```cpp
// 插件接口定义 (interfaces/iplugin.h)
class IPlugin {
public:
    virtual ~IPlugin() {}

    // 核心功能方法
    virtual QString pluginName() const = 0;              // 插件标识名称
    virtual QWidget* createWidget(QWidget* parent = 0) = 0;  // 创建插件UI组件
    virtual bool initialize() = 0;                       // 插件初始化
    virtual void cleanup() = 0;                          // 插件清理

    // 可选的设置界面支持
    virtual bool hasSettings() const { return false; }   // 是否有设置界面
    virtual QWidget* createSettingsWidget(QWidget* parent = 0) { return 0; }  // 创建设置界面
};

Q_DECLARE_INTERFACE(IPlugin, "com.utsmarttrolley.IPlugin")
```

#### 接口说明

**核心方法**：
- `pluginName()`: 返回插件的唯一标识名称，用于插件管理和日志记录
- `createWidget()`: 创建插件的主要UI组件，由主程序负责布局和显示
- `initialize()`: 插件初始化，在插件加载后调用，返回true表示初始化成功
- `cleanup()`: 插件清理，在插件卸载前调用，用于释放资源

**可选方法**：
- `hasSettings()`: 指示插件是否提供设置界面，默认返回false
- `createSettingsWidget()`: 创建设置界面组件，仅在hasSettings()返回true时调用

#### 兼容性说明

**向后兼容**：
- 简化后的接口与现有插件架构完全兼容
- 移除的方法（如pluginVersion）不影响插件的核心功能
- 现有插件只需移除相关方法实现即可适配

**插件管理**：
- PluginManager通过pluginName()进行插件标识和管理
- 插件版本信息可通过qmake项目文件的VERSION变量管理
- 日志记录和调试信息使用pluginName()作为标识

#### 新架构优势

**模块化程度高**：
- 接口与实现完全分离，职责清晰
- 每个功能域有独立的库，便于维护和测试
- 依赖关系明确，避免循环依赖

**开发效率提升**：
- 团队可以并行开发不同的库模块
- 接口稳定后，实现可以独立迭代
- 增量编译，只重新编译变更的库

**系统可维护性**：
- 接口变更影响范围可控
- 库的版本可以独立管理
- 便于进行单元测试和集成测试

**部署灵活性**：
- 可以选择性部署需要的库
- 支持库的热更新（在接口兼容的前提下）
- 清晰的库依赖关系便于问题排查

### 工具类库

系统提供了完整的工具类库支持，包括utlog日志库和ututil通用工具库。

#### UTLog 日志库

基于spdlog的高性能日志库，提供完整的日志记录功能：

```cpp
// utlog.h - 主要接口
namespace utlog {
    namespace log_level {
        enum level_enum {
            trace = 0,
            debug = 1,
            info = 2,
            warn = 3,
            err = 4,
            critical = 5
        };
    }

    // 日志器抽象类
    class UTLogger {
    public:
        virtual bool log(const std::string& data, log_level::level_enum level = log_level::info) = 0;
        virtual bool log(const std::string& data, UTLog_Trace_Info traceInfo, log_level::level_enum level = log_level::info) = 0;
        virtual bool setLevel(log_level::level_enum level) = 0;
    };

    // 核心函数
    std::shared_ptr<UTLogger> createLogger(const std::string& path = "", const std::string& filename = "");
    std::shared_ptr<UTLogger> createDailyLogger(const std::string& path = "", const std::string& filename = "", int maxDays = 7);
    bool setDefultLogger(std::shared_ptr<UTLogger> logger);
    std::shared_ptr<UTLogger> getDefaultLogger();
}

// 便捷宏定义
#define UTLOG_SET_LOGPATH(path)         // 设置日志路径
#define UTLOG_SET_LEVEL(level)          // 设置日志级别
#define UTLOG_SET_CONSOLE_ENABLE(flag)  // 启用控制台输出
#define UTLOG_SET_TRACE_ENABLE(flag)    // 启用位置跟踪

#define UTLOG_TRACE(message)            // 跟踪日志
#define UTLOG_DEBUG(message)            // 调试日志
#define UTLOG_INFO(message)             // 信息日志
#define UTLOG_WARN(message)             // 警告日志
#define UTLOG_ERROR(message)            // 错误日志
#define UTLOG_CRITICAL(message)         // 严重错误日志
```

#### UTUtil 通用工具库

提供26个功能模块的跨平台工具类：

```cpp
// 主要工具模块示例
#include "ututil_string.h"     // 字符串处理
#include "ututil_file.h"       // 文件操作
#include "ututil_thread.h"     // 线程管理
#include "ututil_mutex.h"      // 互斥锁
#include "ututil_time.h"       // 时间工具
#include "ututil_json.h"       // JSON处理
#include "ututil_safemap.h"    // 线程安全容器
#include "ututil_md5.h"        // MD5加密
#include "ututil_ini.h"        // INI配置文件

using namespace ututil;

// 使用示例
std::string upper = CStringUtil::toUpper("hello");
bool exists = CFileUtil::isFileExists("file.txt");
std::string now = CTimeUtil::getDateTimeStr();
CSafeMap<std::string, int> safeMap;  // 线程安全映射
```

## Utils库使用指南

### 库依赖关系

utils库包含两个子库，具有明确的依赖关系：

```
ututil (基础工具库)
  ↑
utlog (日志库，依赖ututil)
```

**构建顺序**：
1. 先构建 `ututil` - 提供基础工具类
2. 再构建 `utlog` - 依赖ututil，提供日志功能

### 在项目中使用Utils库

#### 1. 项目文件配置

在需要使用utils库的模块的.pro文件中添加：

```pro
# 链接utils库
LIBS += -L$$(UTSTENV)/shlib \
        -lutlog \
        -lututil

# 包含头文件路径
INCLUDEPATH += \
    $$(UTSTENV)/utils/utlog/include \
    $$(UTSTENV)/utils/ututil/include
```

#### 2. 日志记录使用

```cpp
#include "utlog.h"

// 在main函数或插件初始化中设置日志
void initializeLogging() {
    UTLOG_SET_LOGPATH("./logs");                    // 设置日志目录
    UTLOG_SET_LEVEL(utlog::log_level::info);        // 设置日志级别
    UTLOG_SET_CONSOLE_ENABLE(true);                 // 启用控制台输出
    UTLOG_SET_TRACE_ENABLE(true);                   // 启用位置跟踪
}

// 在代码中使用日志
void someFunction() {
    UTLOG_INFO("函数开始执行");

    try {
        // 业务逻辑
        UTLOG_DEBUG("处理数据中...");

    } catch (const std::exception& e) {
        UTLOG_ERROR("处理失败: " + std::string(e.what()));
    }

    UTLOG_INFO("函数执行完成");
}
```

#### 3. 通用工具使用

```cpp
#include "ututil_string.h"
#include "ututil_file.h"
#include "ututil_time.h"
#include "ututil_json.h"
#include "ututil_safemap.h"

using namespace ututil;

void utilityExample() {
    // 字符串处理
    std::string text = "Hello World";
    std::string upper = CStringUtil::toUpper(text);
    std::vector<std::string> parts = CStringUtil::split("a,b,c", ",");

    // 文件操作
    if (CFileUtil::isFileExists("config.txt")) {
        std::string content = CFileUtil::readFile("config.txt");
        UTLOG_INFO("配置文件读取成功");
    }

    // 时间工具
    std::string currentTime = CTimeUtil::getDateTimeStr();
    UTLOG_INFO("当前时间: " + currentTime);

    // 线程安全容器
    CSafeMap<std::string, int> safeMap;
    safeMap.insert("key1", 100);
    int value = safeMap.find("key1");

    // JSON处理
    JsonCpp::Value json;
    std::string jsonStr = R"({"name":"test","value":123})";
    if (CJsonUtil::stringToJson(jsonStr.c_str(), &json)) {
        std::string name = json["name"].asString();
        UTLOG_INFO("JSON解析成功: " + name);
    }
}
```

### 最佳实践

1. **统一日志记录**：所有模块都使用UTLOG_*宏进行日志记录
2. **合理使用日志级别**：根据信息重要性选择合适的日志级别
3. **工具类选择**：根据需要选择合适的ututil工具类
4. **线程安全**：在多线程环境中使用CSafe*系列容器
5. **资源管理**：使用ututil提供的智能指针和自动锁

## 数据管理

### 数据流图

下图说明了DataManager如何与各个插件进行数据交互，以及数据采集模块的数据流向：

```mermaid
graph LR
    subgraph "外部数据源"
        SENSORS[传感器设备]
        NETWORK[网络API]
        CAMERAS[摄像头设备]
        FILES[配置文件]
    end

    subgraph "数据采集层"
        DCM[DataCollectionManager<br/>数据采集管理器]
        SA[SensorAdapter<br/>传感器适配器]
        NA[NetworkAdapter<br/>网络适配器]
        CA[CameraAdapter<br/>摄像头适配器]
        FA[FileAdapter<br/>文件适配器]
    end

    subgraph "DataManager核心"
        DM[DataManager<br/>数据管理器]
        VD[VideoData<br/>视频数据]
        SD[StatusData<br/>状态数据]
        DD[DistanceData<br/>距离数据]
        CD[ControlData<br/>控制数据]
        DSD[DebugSettingsData<br/>调试设置数据]
        BLD[BusinessLogicData<br/>业务逻辑数据]
    end
    
    subgraph "功能插件"
        VDP[VideoDisplayPlugin<br/>视频显示插件]
        HAP[HeaderAreaPlugin<br/>顶部区域插件]
        DIP[DistanceInfoPlugin<br/>距离信息插件]
        CAP[ControlAreaPlugin<br/>控制区域插件]
        DSP[DebugSettingsPlugin<br/>调试设置插件]
        BLP[BusinessLogicPlugin<br/>业务逻辑插件]
    end
    
    subgraph "界面显示"
        VW[视频窗口]
        HA[顶部区域<br/>含业务逻辑状态]
        DI[距离信息]
        CA[控制区域<br/>含业务逻辑控制]
        DS[调试设置界面<br/>含业务逻辑配置]
    end

    subgraph "后台服务层"
        BLP[BusinessLogicPlugin<br/>业务逻辑服务<br/>无界面]
        BLE[BusinessLogicEngine<br/>业务逻辑执行引擎]
        BC[BusinessCommands<br/>业务命令集合]
    end
    
    %% 外部数据源到适配器
    SENSORS --> SA
    NETWORK --> NA
    CAMERAS --> CA
    FILES --> FA

    %% 适配器到采集管理器
    SA --> DCM
    NA --> DCM
    CA --> DCM
    FA --> DCM

    %% 采集管理器到数据管理器
    DCM --> DM

    %% DataManager管理数据
    DM --> VD
    DM --> SD
    DM --> DD
    DM --> CD
    DM --> DSD
    DM --> BLD
    
    %% 插件获取数据
    VD --> VDP
    SD --> HAP
    DD --> DIP
    CD --> CAP
    DSD --> DSP
    BLD --> BLP

    %% 插件显示数据
    VDP --> VW
    HAP --> HA
    DIP --> DI
    CAP --> CA
    DSP --> DS
    
    %% 控制数据反馈
    CA --> CD
    DS --> DSD

    %% 业务逻辑服务层
    BLP --> BLE
    BLE --> BC
    BC --> BLE

    %% 插件间服务调用
    CAP -.-> BLP
    HAP -.-> BLP
    DSP -.-> BLP
    
    %% 数据变更通知
    VD -.-> DM
    SD -.-> DM
    DD -.-> DM
    CD -.-> DM
    DSD -.-> DM
    BLD -.-> DM
    
    %% 样式定义
    classDef external fill:#ffebee,stroke:#c62828,stroke-width:2px
    classDef dataCollection fill:#fff8e1,stroke:#f57f17,stroke-width:2px
    classDef dataManager fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef plugin fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef service fill:#e3f2fd,stroke:#1565c0,stroke-width:3px
    classDef ui fill:#e1f5fe,stroke:#01579b,stroke-width:2px

    class SENSORS,NETWORK,CAMERAS,FILES external
    class DCM,SA,NA,CA,FA dataCollection
    class DM,VD,SD,DD,CD,DSD,BLD dataManager
    class VDP,HAP,DIP,CAP,DSP plugin
    class BLP,BLE,BC service
    class VW,HA,DI,CA,DS ui
```

### DataManager接口设计

```cpp
// datamanager.h
class DataManager : public QObject {
    Q_OBJECT
public:
    static DataManager* instance();
    
    // 数据访问接口
    VideoData* videoData() const;
    StatusData* statusData() const;
    DistanceData* distanceData() const;
    ControlData* controlData() const;
    DebugSettingsData* debugSettingsData() const;
    
    // 数据设置接口
    void setVideoData(VideoData* data);
    void setStatusData(StatusData* data);
    void setDistanceData(DistanceData* data);
    void setControlData(ControlData* data);
    void setDebugSettingsData(DebugSettingsData* data);

signals:
    void dataChanged();
    void debugSettingsChanged();

private:
    explicit DataManager(QObject *parent = 0);
    static DataManager *s_instance;
    
    VideoData *m_videoData;
    StatusData *m_statusData;
    DistanceData *m_distanceData;
    ControlData *m_controlData;
    DebugSettingsData *m_debugSettingsData;
};
```

### 数据采集模块设计

#### 模块概述

数据采集模块位于数据层下方，作为整个系统的数据来源支撑。该模块采用适配器模式设计，提供统一的数据采集接口，支持多种数据源类型的接入，并具备良好的可扩展性。

#### 数据采集模块架构图

下图展示了数据采集模块的完整架构设计，包括接口层次、类关系和数据流向：

```mermaid
graph TB
    subgraph "外部数据源层"
        SENSOR[传感器设备<br/>距离/高度/状态]
        API[外部API服务<br/>网络数据源]
        CAMERA[摄像头设备<br/>视频流数据]
        FILE[文件系统<br/>配置/日志文件]
    end

    subgraph "数据采集模块"
        subgraph "接口层"
            IDATA[IDataAdapter<br/>统一适配器接口]
            ABSTRACT[AbstractDataAdapter<br/>抽象基类实现]
        end

        subgraph "适配器实现层"
            SA[SensorAdapter<br/>传感器适配器<br/>• 内置采集逻辑<br/>• 可配置采集间隔]
            NA[NetworkAPIAdapter<br/>网络API适配器<br/>• 内置重试机制<br/>• 可配置超时时间]
            CA[CameraAdapter<br/>摄像头适配器<br/>• 内置帧率控制<br/>• 可配置分辨率]
            FA[FileSystemAdapter<br/>文件系统适配器<br/>• 内置文件监控<br/>• 可配置扫描间隔]
        end

        subgraph "管理层"
            DCM[DataCollectionManager<br/>数据采集管理器<br/>• 适配器注册管理<br/>• 采集模式控制<br/>• 数据分发协调<br/>• 统一采集调度]
        end
    end

    subgraph "数据管理层"
        DM[DataManager<br/>扩展版本<br/>• 数据源绑定<br/>• 自动更新控制]
        VD[VideoData]
        SD[StatusData]
        DD[DistanceData]
        CD[ControlData]
    end

    %% 数据源到适配器的连接
    SENSOR --> SA
    API --> NA
    CAMERA --> CA
    FILE --> FA

    %% 接口继承关系
    IDATA -.-> ABSTRACT
    ABSTRACT -.-> SA
    ABSTRACT -.-> NA
    ABSTRACT -.-> CA
    ABSTRACT -.-> FA

    %% 适配器到管理器
    SA --> DCM
    NA --> DCM
    CA --> DCM
    FA --> DCM

    %% 管理器到数据管理器
    DCM --> DM

    %% 数据管理器到数据模型
    DM --> VD
    DM --> SD
    DM --> DD
    DM --> CD

    %% 数据绑定关系（虚线表示配置绑定）
    DM -.-> SA
    DM -.-> CA
    DM -.-> NA

    %% 样式定义
    classDef external fill:#ffebee,stroke:#c62828,stroke-width:2px
    classDef interface fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef adapter fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
    classDef manager fill:#fff8e1,stroke:#f57f17,stroke-width:3px
    classDef data fill:#fce4ec,stroke:#880e4f,stroke-width:2px

    class SENSOR,API,CAMERA,FILE external
    class IDATA,ABSTRACT interface
    class SA,NA,CA,FA adapter
    class DCM manager
    class DM,VD,SD,DD,CD data
```

#### 架构设计说明

1. **分层设计**：
   - **外部数据源层**：各种类型的数据源，如传感器、API、摄像头等
   - **接口层**：定义统一的适配器接口和抽象基类
   - **适配器实现层**：针对不同数据源类型的具体适配器实现，内置采集逻辑
   - **管理层**：统一的数据采集管理和协调
   - **数据管理层**：与现有系统的集成接口

2. **设计模式应用**：
   - **适配器模式**：IDataAdapter接口统一不同数据源的访问方式
   - **单例模式**：DataCollectionManager提供全局统一管理
   - **观察者模式**：通过Qt信号槽实现状态和数据通知

3. **数据流向和关系**：
   - **实线箭头**：表示数据流向和直接调用关系
     - 外部数据源 → 具体适配器 → 数据采集管理器 → 数据管理器 → 数据模型
   - **虚线箭头**：表示继承关系和配置绑定
     - IDataAdapter → AbstractDataAdapter（接口实现）
     - AbstractDataAdapter → 具体适配器（继承关系）
     - DataManager → 适配器（数据源绑定配置）
   - 支持双向通信：配置下发、状态上报、错误通知

4. **扩展性设计**：
   - 新增数据源类型只需实现IDataAdapter接口
   - 每个适配器内置自己的采集逻辑，可通过配置参数调整采集行为
   - 通过配置文件支持动态适配器注册和参数配置

#### 核心类关系图

下图详细展示了数据采集模块中核心接口和类之间的关系：

```mermaid
classDiagram
    class IDataAdapter {
        <<interface>>
        +AdapterType adapterType()
        +QString adapterId()
        +QString adapterName()
        +bool connect()
        +void disconnect()
        +bool isConnected()
        +AdapterState currentState()
        +bool startCollection()
        +void stopCollection()
        +QVariantMap collectData()
        +void setConfiguration(config)
        +QVariantMap getConfiguration()
        +stateChanged(newState, oldState)
        +dataCollected(data)
        +errorOccurred(error)
    }

    class AbstractDataAdapter {
        <<abstract>>
        -QString m_adapterId
        -AdapterState m_currentState
        -bool m_isConnected
        -QVariantMap m_configuration
        -QMutex m_adapterMutex
        +QString adapterId()
        +AdapterState currentState()
        +bool isConnected()
        +void setConfiguration(config)
        +QVariantMap getConfiguration()
        #doConnect()* bool
        #doDisconnect()* void
        #doCollectData()* QVariantMap
        #setState(newState)
        #setLastError(error)
    }

    class SensorAdapter {
        -QSerialPort* m_serialPort
        -QString m_portName
        -int m_baudRate
        -QByteArray m_dataBuffer
        +QString adapterName()
        +AdapterType adapterType()
        +QVariantMap collectData()
        #doConnect() bool
        #doDisconnect() void
        #doCollectData() QVariantMap
        -setupSerialPort()
        -parseSensorData(rawData)
    }

    class NetworkAPIAdapter {
        -QNetworkAccessManager* m_networkManager
        -QString m_baseUrl
        -QString m_apiKey
        -int m_timeout
        +QString adapterName()
        +AdapterType adapterType()
        #doConnect() bool
        #doCollectData() QVariantMap
        -setupNetworkManager()
        -createRequest(endpoint)
        -parseApiResponse(response)
    }

    class CameraAdapter {
        -QCamera* m_camera
        -QCameraImageCapture* m_imageCapture
        -int m_deviceIndex
        -QString m_resolution
        +QString adapterName()
        +AdapterType adapterType()
        #doConnect() bool
        #doCollectData() QVariantMap
        -setupCamera()
        -captureFrame()
    }

    class DataCollectionManager {
        <<singleton>>
        -static DataCollectionManager* s_instance
        -QMap~QString, IDataAdapter*~ m_adapters
        -CollectionMode m_collectionMode
        -QMutex m_managerMutex
        -QTimer* m_schedulerTimer
        +static instance() DataCollectionManager*
        +registerAdapter(adapter) bool
        +unregisterAdapter(adapterId) bool
        +getAdapter(adapterId) IDataAdapter*
        +setCollectionMode(mode)
        +startCollection(adapterId) bool
        +stopCollection(adapterId)
        +getLatestData(adapterId) QVariantMap
        +setCollectionInterval(adapterId, intervalMs)
        +getCollectionInterval(adapterId) int
        +adapterRegistered(adapterId)
        +dataCollected(adapterId, data)
        +collectionError(adapterId, error)
    }

    %% 继承关系
    IDataAdapter <|.. AbstractDataAdapter : implements
    AbstractDataAdapter <|-- SensorAdapter : extends
    AbstractDataAdapter <|-- NetworkAPIAdapter : extends
    AbstractDataAdapter <|-- CameraAdapter : extends

    %% 组合关系
    DataCollectionManager o-- IDataAdapter : manages

    %% 依赖关系
    SensorAdapter ..> QSerialPort : uses
    NetworkAPIAdapter ..> QNetworkAccessManager : uses
    CameraAdapter ..> QCamera : uses
```

#### 数据采集流程时序图

下图展示了数据采集模块的典型工作流程：

```mermaid
sequenceDiagram
    participant App as 应用程序
    participant DM as DataManager
    participant DCM as DataCollectionManager
    participant SA as SensorAdapter
    participant Device as 外部设备

    Note over App,Device: 系统初始化阶段
    App->>DCM: 创建并注册适配器
    DCM->>SA: registerAdapter(sensorAdapter)
    App->>DM: 绑定数据源
    DM->>DCM: bindDataSource("distance", "sensor_001")
    App->>DCM: 配置采集参数
    DCM->>SA: setConfiguration({interval: 1000})

    Note over App,Device: 数据采集启动
    App->>DCM: startCollection()
    DCM->>SA: startCollection()
    SA->>Device: connect()
    Device-->>SA: 连接确认
    SA->>SA: 启动内部定时器
    SA->>DCM: emit stateChanged(Connected)
    DCM->>DM: emit adapterRegistered("sensor_001")

    Note over App,Device: 数据采集循环
    loop 采集循环
        Note over SA: 内部定时器触发
        SA->>SA: checkCollectionTime()
        SA->>Device: 请求数据
        Device-->>SA: 返回原始数据
        SA->>SA: parseSensorData(rawData)
        SA->>DCM: emit dataCollected(processedData)
        DCM->>DM: emit dataCollected("sensor_001", data)
        DM->>DM: updateDistanceData(data)
        DM->>App: emit dataChanged()

        Note over SA: 等待下次采集间隔
        SA->>SA: resetCollectionTimer()
    end

    Note over App,Device: 错误处理流程
    SA->>Device: 请求数据
    Device-->>SA: 连接错误
    SA->>SA: handleError()
    SA->>DCM: emit errorOccurred("连接超时")
    DCM->>DM: emit collectionError("sensor_001", error)
    SA->>SA: 尝试重连

    Note over App,Device: 系统关闭
    App->>DCM: stopCollection()
    DCM->>SA: stopCollection()
    SA->>SA: 停止内部定时器
    SA->>Device: disconnect()
    SA->>DCM: emit stateChanged(Disconnected)
```

#### 核心接口设计

##### 数据适配器接口

```cpp
// 数据适配器接口 (interfaces/idataadapter.h)
class IDataAdapter : public QObject
{
    Q_OBJECT

public:
    enum AdapterType {
        SensorType = 0,     // 传感器类型
        NetworkType = 1,    // 网络API类型
        CameraType = 2,     // 摄像头类型
        FileType = 3        // 文件系统类型
    };

    enum AdapterState {
        Disconnected = 0,   // 未连接
        Connected = 1,      // 已连接
        Error = 2           // 错误状态
    };

    virtual ~IDataAdapter() {}

    // 基本信息
    virtual QString adapterId() const = 0;
    virtual QString adapterName() const = 0;
    virtual AdapterType adapterType() const = 0;

    // 连接管理
    virtual bool connect() = 0;
    virtual void disconnect() = 0;
    virtual bool isConnected() const = 0;
    virtual AdapterState currentState() const = 0;

    // 数据采集
    virtual bool startCollection() = 0;
    virtual void stopCollection() = 0;
    virtual QVariantMap collectData() = 0;

    // 配置管理
    virtual void setConfiguration(const QVariantMap& config) = 0;
    virtual QVariantMap getConfiguration() const = 0;

signals:
    void stateChanged(AdapterState newState, AdapterState oldState);
    void dataCollected(const QVariantMap& data);
    void errorOccurred(const QString& errorMessage);
};

Q_DECLARE_INTERFACE(IDataAdapter, "com.utsmarttrolley.IDataAdapter")
```

##### 数据采集管理器

```cpp
// 数据采集管理器 (datacollection/datacollectionmanager.h)
class DataCollectionManager : public QObject
{
    Q_OBJECT

public:
    enum CollectionMode {
        RealTime = 0,       // 实时采集
        Scheduled = 1,      // 定时采集
        OnDemand = 2        // 按需采集
    };

    static DataCollectionManager* instance();

    // 适配器管理
    bool registerAdapter(IDataAdapter* adapter);
    bool unregisterAdapter(const QString& adapterId);
    IDataAdapter* getAdapter(const QString& adapterId) const;
    QStringList getRegisteredAdapters() const;

    // 采集控制
    void setCollectionMode(CollectionMode mode);
    bool startCollection(const QString& adapterId = QString());
    void stopCollection(const QString& adapterId = QString());
    bool isCollecting() const;

    // 采集配置
    void setCollectionInterval(const QString& adapterId, int intervalMs);
    int getCollectionInterval(const QString& adapterId) const;
    void setAdapterConfiguration(const QString& adapterId, const QVariantMap& config);
    QVariantMap getAdapterConfiguration(const QString& adapterId) const;

    // 数据访问
    QVariantMap getLatestData(const QString& adapterId) const;

signals:
    void adapterRegistered(const QString& adapterId);
    void dataCollected(const QString& adapterId, const QVariantMap& data);
    void collectionError(const QString& adapterId, const QString& error);

private:
    explicit DataCollectionManager(QObject* parent = 0);
    static DataCollectionManager* s_instance;

    QMap<QString, IDataAdapter*> m_adapters;
    CollectionMode m_collectionMode;
    QMutex m_managerMutex;
};
```

#### 适配器实现

##### 传感器适配器

```cpp
// 传感器适配器 (datacollection/sensoradapter.h)
class SensorAdapter : public AbstractDataAdapter
{
    Q_OBJECT

public:
    explicit SensorAdapter(QObject* parent = 0);

    // IDataAdapter接口实现
    virtual QString adapterName() const { return "Sensor Data Adapter"; }
    virtual AdapterType adapterType() const { return SensorType; }
    virtual QVariantMap collectData();

    // 采集配置
    void setCollectionInterval(int intervalMs);
    int collectionInterval() const;

protected:
    virtual bool doConnect();
    virtual void doDisconnect();
    virtual QVariantMap doCollectData();

private slots:
    void onCollectionTimer();

private:
    QSerialPort* m_serialPort;
    QString m_portName;
    int m_baudRate;
    QTimer* m_collectionTimer;
    int m_collectionInterval;
};
```

##### 网络API适配器

```cpp
// 网络API适配器 (datacollection/networkapiadapter.h)
class NetworkAPIAdapter : public AbstractDataAdapter
{
    Q_OBJECT

public:
    explicit NetworkAPIAdapter(QObject* parent = 0);

    virtual QString adapterName() const { return "Network API Adapter"; }
    virtual AdapterType adapterType() const { return NetworkType; }

protected:
    virtual bool doConnect();
    virtual QVariantMap doCollectData();

private:
    QNetworkAccessManager* m_networkManager;
    QString m_baseUrl;
    QString m_apiKey;
};
```



#### 与现有系统集成

数据采集模块通过扩展DataManager与现有系统无缝集成：

```cpp
// 扩展后的DataManager
class DataManager : public QObject {
    Q_OBJECT
public:
    // 原有接口保持不变
    VideoData* videoData() const;
    StatusData* statusData() const;
    DistanceData* distanceData() const;

    // 新增：数据采集管理接口
    DataCollectionManager* dataCollectionManager() const;
    void setDataCollectionManager(DataCollectionManager* manager);

    // 新增：数据源绑定接口
    void bindDataSource(const QString& dataType, const QString& adapterId);
    QString getBoundDataSource(const QString& dataType) const;

private:
    DataCollectionManager* m_dataCollectionManager;
    QMap<QString, QString> m_dataSourceBindings;
};
```

## 业务逻辑执行模块设计

### 功能概述

业务逻辑执行模块是一个**纯后台服务组件**，专注于执行各种智能运载小车的业务操作，如自动对齐、路径规划、设备校准等。该模块采用**服务导向架构（SOA）**设计，作为无界面的后台服务为其他插件提供业务逻辑执行能力。

#### 设计理念

1. **服务导向** - 作为后台服务提供者，不包含任何用户界面元素
2. **单一职责** - 专注于业务命令的执行、队列管理和状态监控
3. **API驱动** - 通过标准API接口为其他插件提供服务
4. **松耦合** - 与界面层完全分离，通过插件间协作实现功能

#### 主要功能特性

1. **业务操作执行** - 支持多种预定义和自定义业务操作
2. **命令队列管理** - 支持操作的排队、优先级管理和并发执行
3. **状态监控** - 实时监控操作状态和执行进度
4. **中断控制** - 支持操作的暂停、恢复和取消
5. **结果反馈** - 提供详细的执行结果和错误信息
6. **历史记录** - 记录操作历史和统计信息
7. **服务API** - 为其他插件提供完整的调用接口

#### 业务逻辑服务架构图

下图展示了业务逻辑执行模块架构，突出其作为后台服务的特性以及与其他插件的协作关系：

```mermaid
graph TB
    subgraph "后台服务层"
        BLP[BusinessLogicPlugin<br/>业务逻辑服务插件<br/>无界面]
        BLE[BusinessLogicEngine<br/>业务逻辑执行引擎]
        API[Service API<br/>服务接口]
    end

    subgraph "命令执行层"
        IBC[IBusinessCommand<br/>业务命令接口]
        ABC[AbstractBusinessCommand<br/>抽象命令基类]

        AAC[AutoAlignmentCommand<br/>自动对齐命令]
        PPC[PathPlanningCommand<br/>路径规划命令]
        DCC[DeviceCalibrationCommand<br/>设备校准命令]
        SCC[SystemCheckCommand<br/>系统检查命令]
    end

    subgraph "用户界面层"
        CAP[ControlAreaPlugin<br/>控制区域插件]
        CAW[ControlAreaWidget<br/>业务操作控制界面]

        HAP[HeaderAreaPlugin<br/>顶部区域插件]
        HAW[HeaderAreaWidget<br/>顶部区域显示]

        DSP[DebugSettingsPlugin<br/>调试设置插件]
        BLCD[BusinessLogicConfigDialog<br/>业务逻辑配置界面]
    end

    subgraph "数据管理层"
        DM[DataManager<br/>数据管理器]
        BLD[BusinessLogicData<br/>业务逻辑数据]
        UTLOG[UTLog<br/>日志记录库]
    end

    %% 服务提供关系
    BLP --> BLE
    BLP --> API
    BLE --> IBC

    %% 命令实现关系
    ABC -.-> IBC
    AAC -.-> ABC
    PPC -.-> ABC
    DCC -.-> ABC
    SCC -.-> ABC

    %% 服务调用关系
    CAP --> API
    HAP --> API
    DSP --> API

    %% 界面组件关系
    CAP --> CAW
    HAP --> HAW
    DSP --> BLCD

    %% 数据管理关系
    BLP --> DM
    BLP --> BLD
    BLE --> LM
    DM --> BLD

    %% 状态通知关系
    BLP -.-> CAW
    BLP -.-> HAW
    BLP -.-> BLCD

    %% 样式定义
    classDef service fill:#e3f2fd,stroke:#1565c0,stroke-width:3px
    classDef command fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef interface fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
    classDef ui fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef data fill:#fce4ec,stroke:#c2185b,stroke-width:2px

    class BLP,BLE,API service
    class IBC,ABC interface
    class AAC,PPC,DCC,SCC command
    class CAP,CAW,HAP,HAW,DSP,BLCD ui
    class DM,BLD data
    class UTLOG utils
```

#### 插件协作关系图

下图详细说明了业务逻辑服务与其他插件的协作方式：

```mermaid
sequenceDiagram
    participant CAW as ControlAreaWidget
    participant BLP as BusinessLogicPlugin
    participant BLE as BusinessLogicEngine
    participant HAW as HeaderAreaWidget
    participant DM as DataManager

    Note over CAW,DM: 业务命令执行流程

    CAW->>BLP: executeCommand("AutoAlignment")
    BLP->>BLE: 获取命令并执行
    BLE->>BLE: 命令状态变更
    BLE->>BLP: 发送状态信号
    BLP->>CAW: commandStarted信号
    BLP->>HAW: commandStarted信号
    BLP->>DM: 更新业务逻辑数据

    Note over BLE: 命令执行中...

    BLE->>BLP: progressChanged信号
    BLP->>CAW: 更新进度显示
    BLP->>HAW: 更新顶部区域进度

    BLE->>BLP: commandCompleted信号
    BLP->>CAW: 显示执行结果
    BLP->>HAW: 更新状态为就绪
    BLP->>DM: 保存执行历史
```

### 设计模式应用

#### 命令模式（Command Pattern）

业务逻辑模块的核心采用命令模式，将每个业务操作封装为独立的命令对象：

```cpp
// 命令模式的优势
// 1. 操作封装：每个业务操作都是独立的命令对象
// 2. 撤销支持：支持操作的撤销和重做
// 3. 队列管理：命令可以排队、延迟执行
// 4. 日志记录：所有操作都可以被记录和回放
// 5. 宏命令：支持组合多个命令为复合操作
```

#### 状态模式（State Pattern）

每个命令都有明确的状态管理，支持状态转换和状态相关的行为：

```cpp
// 命令状态转换图
// Ready -> Running -> Completed/Failed/Cancelled
//   |         |
//   |         v
//   |      Paused
//   |         |
//   |         v
//   |      Running
//   |
//   v
// Cancelled
```

#### 观察者模式（Observer Pattern）

通过Qt的信号槽机制实现观察者模式，提供实时的状态和进度反馈：

```cpp
// 观察者模式应用
// 1. 状态变化通知：命令状态改变时通知所有观察者
// 2. 进度更新：实时更新执行进度
// 3. 错误报告：异常情况的及时通知
// 4. 完成通知：操作完成时的结果反馈
```

## 调试设置功能组件设计

### 功能概述

调试设置功能组件是一个独立的插件模块，提供系统调试和配置管理功能。该组件通过主界面右上角的"调试设置"按钮触发，弹出独立的设置界面对话框，包含6个主要功能模块：

1. **设置开关柜参数** - 配置开关柜相关参数
2. **设置调试参数** - 配置系统调试参数
3. **程序升级** - 系统程序升级管理
4. **设备日志** - 查看和管理设备日志
5. **设备自检** - 执行设备自检功能
6. **运动机构原点矫正** - 运动机构校准功能

#### 调试设置功能模块图

下图展示了调试设置功能组件的内部结构，包括6个子功能模块及其相互关系：

```mermaid
graph TB
    subgraph "主界面"
        MW[MainWindow<br/>主窗口]
        DSB[DebugSettingsButton<br/>调试设置按钮]
    end

    subgraph "调试设置插件"
        DSP[DebugSettingsPlugin<br/>调试设置插件]
        DSD_Main[DebugSettingsDialog<br/>主设置对话框]
    end

    subgraph "6个功能模块"
        SCSD[SwitchCabinetSettingsDialog<br/>设置开关柜参数]
        DPD[DebugParametersDialog<br/>设置调试参数]
        PUD[ProgramUpgradeDialog<br/>程序升级]
        DLD[DeviceLogsDialog<br/>设备日志]
        DSCD[DeviceSelfCheckDialog<br/>设备自检]
        MCD[MotionCalibrationDialog<br/>运动机构原点矫正]
    end

    subgraph "数据管理"
        DSD_Data[DebugSettingsData<br/>调试设置数据]
        SCS[SwitchCabinetSettings<br/>开关柜设置]
        DP[DebugParameters<br/>调试参数]
    end

    subgraph "工具类支持"
        UTLOG[UTLog<br/>日志记录库]
    end

    %% 主要交互流程
    MW --> DSB
    DSB --> DSD_Main
    DSP --> DSD_Main

    %% 功能模块关系
    DSD_Main --> SCSD
    DSD_Main --> DPD
    DSD_Main --> PUD
    DSD_Main --> DLD
    DSD_Main --> DSCD
    DSD_Main --> MCD

    %% 数据关系
    DSP --> DSD_Data
    DSD_Data --> SCS
    DSD_Data --> DP

    %% 各模块使用数据
    SCSD --> SCS
    DPD --> DP
    DLD --> UTLOG

    %% 样式定义
    classDef mainUI fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef plugin fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef dialog fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef data fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef utils fill:#fce4ec,stroke:#c2185b,stroke-width:2px

    class MW,DSB mainUI
    class DSP,DSD_Main plugin
    class SCSD,DPD,PUD,DLD,DSCD,MCD dialog
    class DSD_Data,SCS,DP data
    class UTLOG utils
```

### 架构集成

#### 插件化架构集成

```cpp
// debugsettingsplugin.h
class DebugSettingsPlugin : public QObject, public IPlugin
{
    Q_OBJECT
    Q_INTERFACES(IPlugin)

public:
    explicit DebugSettingsPlugin(QObject *parent = 0);
    virtual ~DebugSettingsPlugin();

    // IPlugin接口实现
    virtual QString pluginName() const;
    virtual QWidget* createWidget(QWidget* parent = 0);
    virtual bool initialize();
    virtual void cleanup();
    virtual bool hasSettings() const { return true; }
    virtual QWidget* createSettingsWidget(QWidget* parent = 0);

private slots:
    void showDebugSettings();

private:
    DataManager *m_dataManager;
    DebugSettingsWidget *m_widget;
    QAction *m_debugSettingsAction;
};
```

#### 主界面集成

```cpp
// mainwindow.h 中添加调试设置按钮
class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    explicit MainWindow(QWidget *parent = 0);

private slots:
    void showDebugSettings();

private:
    void setupDebugSettingsButton();
    QPushButton *m_debugSettingsButton;
    DebugSettingsDialog *m_debugSettingsDialog;
};
```

### 界面设计

#### 界面层次结构图

下图展示了主界面、调试设置对话框以及各个子设置界面之间的层次关系：

```mermaid
graph TD
    subgraph "主应用程序界面"
        MW[MainWindow<br/>主窗口<br/>1024x768]
        TB[TitleBar<br/>标题栏]
        DSB[DebugSettingsButton<br/>调试设置按钮<br/>右上角]
    end

    subgraph "插件界面区域"
        HAW[HeaderAreaWidget<br/>顶部区域<br/>顶部]
        VDW[VideoDisplayWidget<br/>视频显示<br/>中央左侧]
        DIW[DistanceInfoWidget<br/>距离信息<br/>中央右侧]
        CAW[ControlAreaWidget<br/>控制区域<br/>右侧]
    end

    subgraph "调试设置主界面"
        DSD[DebugSettingsDialog<br/>调试设置对话框<br/>800x600<br/>模态对话框]

        subgraph "功能按钮网格 (2x3)"
            SCB[设置开关柜参数<br/>按钮]
            DPB[设置调试参数<br/>按钮]
            PUB[程序升级<br/>按钮]
            DLB[设备日志<br/>按钮]
            DSC[设备自检<br/>按钮]
            MCB[运动机构原点矫正<br/>按钮]
        end
    end

    subgraph "子设置界面"
        SCSD[SwitchCabinetSettingsDialog<br/>开关柜参数设置<br/>600x400]
        DPD[DebugParametersDialog<br/>调试参数设置<br/>700x500<br/>标签页界面]
        PUD[ProgramUpgradeDialog<br/>程序升级<br/>600x450<br/>进度条界面]
        DLD[DeviceLogsDialog<br/>设备日志<br/>800x600<br/>日志查看器]
        DSCD[DeviceSelfCheckDialog<br/>设备自检<br/>500x400<br/>检测界面]
        MCD[MotionCalibrationDialog<br/>运动机构校准<br/>600x500<br/>校准界面]
    end

    subgraph "调试参数子标签页"
        LT[LoggingTab<br/>日志设置]
        CT[CommunicationTab<br/>通信设置]
        PT[PerformanceTab<br/>性能设置]
    end

    %% 主界面组成
    MW --> TB
    MW --> DSB
    MW --> HAW
    MW --> VDW
    MW --> DIW
    MW --> CAW

    %% 调试设置界面层次
    DSB --> DSD
    DSD --> SCB
    DSD --> DPB
    DSD --> PUB
    DSD --> DLB
    DSD --> DSC
    DSD --> MCB

    %% 子界面调用关系
    SCB --> SCSD
    DPB --> DPD
    PUB --> PUD
    DLB --> DLD
    DSC --> DSCD
    MCB --> MCD

    %% 调试参数标签页
    DPD --> LT
    DPD --> CT
    DPD --> PT

    %% 样式定义
    classDef mainWindow fill:#e3f2fd,stroke:#1565c0,stroke-width:3px
    classDef pluginWidget fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef debugDialog fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
    classDef button fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef subDialog fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef tab fill:#f1f8e9,stroke:#689f38,stroke-width:2px

    class MW,TB,DSB mainWindow
    class HAW,VDW,DIW,CAW pluginWidget
    class DSD debugDialog
    class SCB,DPB,PUB,DLB,DSC,MCB button
    class SCSD,DPD,PUD,DLD,DSCD,MCD subDialog
    class LT,CT,PT tab
```

#### 设置对话框主界面

```cpp
// debugsettingsdialog.h
class DebugSettingsDialog : public QDialog
{
    Q_OBJECT

public:
    explicit DebugSettingsDialog(QWidget *parent = 0);
    ~DebugSettingsDialog();

    void setDataManager(DataManager *dataManager);

private slots:
    void openSwitchCabinetSettings();
    void openDebugParameters();
    void openProgramUpgrade();
    void openDeviceLogs();
    void openDeviceSelfCheck();
    void openMotionCalibration();

private:
    void setupUI();
    void setupConnections();
    QPushButton* createSettingsButton(const QString &text);

private:
    DataManager *m_dataManager;

    // 功能按钮
    QPushButton *m_switchCabinetButton;
    QPushButton *m_debugParamsButton;
    QPushButton *m_upgradeButton;
    QPushButton *m_deviceLogsButton;
    QPushButton *m_selfCheckButton;
    QPushButton *m_motionCalibButton;

    // 子对话框指针
    class SwitchCabinetSettingsDialog *m_switchCabinetDialog;
    class DebugParametersDialog *m_debugParamsDialog;
    class ProgramUpgradeDialog *m_upgradeDialog;
    class DeviceLogsDialog *m_deviceLogsDialog;
    class DeviceSelfCheckDialog *m_selfCheckDialog;
    class MotionCalibrationDialog *m_motionCalibDialog;
};
```

### 功能模块设计

#### 1. 设置开关柜参数模块

```cpp
// switchcabinetsettingsdialog.h
class SwitchCabinetSettingsDialog : public QDialog
{
    Q_OBJECT

public:
    explicit SwitchCabinetSettingsDialog(QWidget *parent = 0);
    void setDataManager(DataManager *dataManager);

private slots:
    void loadSettings();
    void saveSettings();
    void resetToDefaults();

private:
    void setupUI();
    DataManager *m_dataManager;

    // 开关柜参数控件
    QSpinBox *m_voltageSpinBox;
    QSpinBox *m_currentSpinBox;
    QDoubleSpinBox *m_powerSpinBox;
    QComboBox *m_modeComboBox;
    QSpinBox *m_timeoutSpinBox;
};
```

#### 2. 设置调试参数模块

```cpp
// debugparametersdialog.h
class DebugParametersDialog : public QDialog
{
    Q_OBJECT

public:
    explicit DebugParametersDialog(QWidget *parent = 0);
    void setDataManager(DataManager *dataManager);

private slots:
    void loadDebugSettings();
    void saveDebugSettings();
    void onLogLevelChanged();

private:
    void setupUI();
    void setupLoggingTab();
    void setupCommunicationTab();
    void setupPerformanceTab();

private:
    DataManager *m_dataManager;
    QTabWidget *m_tabWidget;

    // 日志设置
    QComboBox *m_logLevelComboBox;
    QCheckBox *m_fileLoggingCheckBox;
    QCheckBox *m_consoleLoggingCheckBox;
    QSpinBox *m_maxLogFilesSpinBox;

    // 通信设置
    QCheckBox *m_debugCommunicationCheckBox;
    QSpinBox *m_communicationTimeoutSpinBox;
    QSpinBox *m_retryCountSpinBox;

    // 性能设置
    QCheckBox *m_performanceMonitoringCheckBox;
    QSpinBox *m_updateIntervalSpinBox;
    QCheckBox *m_memoryMonitoringCheckBox;
};
```

#### 3. 程序升级模块

```cpp
// programupgradedialog.h
class ProgramUpgradeDialog : public QDialog
{
    Q_OBJECT

public:
    explicit ProgramUpgradeDialog(QWidget *parent = 0);

private slots:
    void selectUpgradeFile();
    void startUpgrade();
    void cancelUpgrade();
    void onUpgradeProgress(int percentage);
    void onUpgradeFinished(bool success, const QString &message);

private:
    void setupUI();
    void resetUI();

private:
    QString m_upgradeFilePath;

    QLabel *m_filePathLabel;
    QPushButton *m_selectFileButton;
    QPushButton *m_startUpgradeButton;
    QPushButton *m_cancelButton;
    QProgressBar *m_progressBar;
    QTextEdit *m_logTextEdit;

    bool m_upgradeInProgress;
};
```

#### 4. 设备日志模块

```cpp
// devicelogsdialog.h
class DeviceLogsDialog : public QDialog
{
    Q_OBJECT

public:
    explicit DeviceLogsDialog(QWidget *parent = 0);

private slots:
    void refreshLogs();
    void clearLogs();
    void exportLogs();
    void onLogLevelFilterChanged();

private:
    void setupUI();
    void loadLogs();

private:
    // 使用UTLog库进行日志记录，无需单独的LogManager实例
    QTextEdit *m_logTextEdit;
    QComboBox *m_logLevelFilter;
    QPushButton *m_refreshButton;
    QPushButton *m_clearButton;
    QPushButton *m_exportButton;
};
```

#### 5. 设备自检模块

```cpp
// deviceselfcheckdialog.h
class DeviceSelfCheckDialog : public QDialog
{
    Q_OBJECT

public:
    explicit DeviceSelfCheckDialog(QWidget *parent = 0);

private slots:
    void startSelfCheck();
    void stopSelfCheck();
    void onCheckProgress(const QString &item, bool result);
    void onCheckFinished();

private:
    void setupUI();
    void resetUI();

private:
    QListWidget *m_checkListWidget;
    QPushButton *m_startButton;
    QPushButton *m_stopButton;
    QProgressBar *m_progressBar;
    QLabel *m_statusLabel;

    bool m_checkInProgress;
};
```

#### 6. 运动机构原点矫正模块

```cpp
// motioncalibrationdialog.h
class MotionCalibrationDialog : public QDialog
{
    Q_OBJECT

public:
    explicit MotionCalibrationDialog(QWidget *parent = 0);

private slots:
    void startCalibration();
    void stopCalibration();
    void onCalibrationProgress(const QString &axis, double position);
    void onCalibrationFinished(bool success);

private:
    void setupUI();
    void resetUI();

private:
    QGroupBox *m_xAxisGroup;
    QGroupBox *m_yAxisGroup;
    QGroupBox *m_zAxisGroup;
    QPushButton *m_startButton;
    QPushButton *m_stopButton;
    QProgressBar *m_progressBar;

    bool m_calibrationInProgress;
};
```

### 数据管理集成

#### 设置数据结构

```cpp
// debugsettingsdata.h
struct SwitchCabinetSettings
{
    int voltage;
    int current;
    double power;
    QString mode;
    int timeout;

    SwitchCabinetSettings()
        : voltage(220), current(10), power(2.2), mode("Auto"), timeout(30) {}
};

struct DebugParameters
{
    QString logLevel;
    bool fileLogging;
    bool consoleLogging;
    int maxLogFiles;
    bool debugCommunication;
    int communicationTimeout;
    int retryCount;
    bool performanceMonitoring;
    int updateInterval;
    bool memoryMonitoring;

    DebugParameters()
        : logLevel("Info"), fileLogging(true), consoleLogging(false)
        , maxLogFiles(10), debugCommunication(false), communicationTimeout(5000)
        , retryCount(3), performanceMonitoring(false), updateInterval(1000)
        , memoryMonitoring(false) {}
};

class DebugSettingsData : public QObject
{
    Q_OBJECT

public:
    explicit DebugSettingsData(QObject *parent = 0);

    // 开关柜设置
    SwitchCabinetSettings switchCabinetSettings() const;
    void setSwitchCabinetSettings(const SwitchCabinetSettings &settings);

    // 调试参数
    DebugParameters debugParameters() const;
    void setDebugParameters(const DebugParameters &params);

    // 数据持久化
    void loadFromConfig();
    void saveToConfig();

signals:
    void switchCabinetSettingsChanged();
    void debugParametersChanged();

private:
    SwitchCabinetSettings m_switchCabinetSettings;
    DebugParameters m_debugParameters;
};
```

### 扩展性设计

#### 类关系图

下图展示了关键类之间的继承和依赖关系，特别是插件接口、数据管理类和工具类之间的关系：

```mermaid
classDiagram
    %% 核心接口和基类
    class QObject {
        +signals/slots
        +parent/child relationship
    }

    class QWidget {
        +show()
        +hide()
        +paintEvent()
    }

    class QDialog {
        +exec()
        +accept()
        +reject()
    }

    %% 插件接口
    class IPlugin {
        <<interface>>
        +pluginName() QString
        +createWidget() QWidget*
        +initialize() bool
        +cleanup() void
        +hasSettings() bool
        +createSettingsWidget() QWidget*
    }

    %% 数据管理核心
    class DataManager {
        -m_videoData: VideoData*
        -m_statusData: StatusData*
        -m_distanceData: DistanceData*
        -m_controlData: ControlData*
        -m_debugSettingsData: DebugSettingsData*
        +videoData() VideoData*
        +statusData() StatusData*
        +debugSettingsData() DebugSettingsData*
        +setDebugSettingsData(DebugSettingsData*)
    }

    %% 数据模型类
    class DebugSettingsData {
        -m_switchCabinetSettings: SwitchCabinetSettings
        -m_debugParameters: DebugParameters
        +switchCabinetSettings() SwitchCabinetSettings
        +setSwitchCabinetSettings(SwitchCabinetSettings)
        +loadFromConfig()
        +saveToConfig()
    }

    class BusinessLogicData {
        -m_commandStatuses: QMap~QString,CommandStatus~
        -m_executionHistory: QList~CommandStatus~
        -m_statistics: ExecutionStatistics
        +updateCommandStatus(CommandStatus)
        +getCommandStatus(QString) CommandStatus
        +getExecutionStatistics() ExecutionStatistics
        +saveToConfig()
        +loadFromConfig()
    }

    %% 具体插件实现
    class DebugSettingsPlugin {
        -m_dataManager: DataManager*
        -m_widget: DebugSettingsWidget*
        -m_debugSettingsAction: QAction*
        +initialize() bool
        +createWidget() QWidget*
        +showDebugSettings()
    }

    class BusinessLogicPlugin {
        -m_dataManager: DataManager*
        -m_engine: BusinessLogicEngine*
        -m_widget: BusinessLogicWidget*
        +initialize() bool
        +createWidget() QWidget*
        +registerCommand(IBusinessCommand*)
        +getEngine() BusinessLogicEngine*
    }

    %% 界面组件
    class MainWindow {
        -m_pluginManager: PluginManager*
        -m_dataManager: DataManager*
        -m_debugSettingsButton: QPushButton*
        -m_debugSettingsDialog: DebugSettingsDialog*
        +setupDebugSettingsButton()
        +showDebugSettings()
    }

    class DebugSettingsDialog {
        -m_dataManager: DataManager*
        -m_switchCabinetButton: QPushButton*
        -m_debugParamsButton: QPushButton*
        +setDataManager(DataManager*)
        +setupUI()
        +openSwitchCabinetSettings()
    }

    %% 业务逻辑核心类
    class BusinessLogicEngine {
        -m_commands: QMap~QString,CommandInfo~
        -m_commandQueue: QQueue~QString~
        -m_executionMode: ExecutionMode
        -m_maxConcurrentCommands: int
        +addCommand(IBusinessCommand*) QString
        +enqueueCommand(QString,int) bool
        +startExecution()
        +pauseExecution()
        +stopExecution()
    }

    class IBusinessCommand {
        <<interface>>
        +commandId() QString
        +commandName() QString
        +currentState() CommandState
        +currentProgress() int
        +execute()
        +pause()
        +cancel()
    }

    class AbstractBusinessCommand {
        -m_commandId: QString
        -m_currentState: CommandState
        -m_currentProgress: int
        +setState(CommandState)
        +setProgress(int)
        +doExecute()
        +doPause()
        +doCancel()
    }

    %% 工具类库
    class UTLog {
        <<library>>
        +createLogger() shared_ptr~UTLogger~
        +createDailyLogger() shared_ptr~UTLogger~
        +setDefultLogger() bool
        +getDefaultLogger() shared_ptr~UTLogger~
        +UTLOG_INFO(message)
        +UTLOG_DEBUG(message)
        +UTLOG_ERROR(message)
    }

    class UTUtil {
        <<library>>
        +CStringUtil::toUpper() string
        +CFileUtil::isFileExists() bool
        +CTimeUtil::getDateTimeStr() string
        +CSafeMap~K,V~
        +CMD5Util::getMD5() string
    }

    %% 继承关系
    QObject <|-- DataManager
    QObject <|-- DebugSettingsData
    QObject <|-- BusinessLogicData
    QObject <|-- DebugSettingsPlugin
    QObject <|-- BusinessLogicPlugin
    QObject <|-- BusinessLogicEngine
    QObject <|-- AbstractBusinessCommand
    %% UTLog和UTUtil是库，不继承QObject

    QWidget <|-- QDialog
    QWidget <|-- MainWindow
    QDialog <|-- DebugSettingsDialog

    %% 接口实现
    IPlugin <|.. DebugSettingsPlugin
    IPlugin <|.. BusinessLogicPlugin
    IBusinessCommand <|.. AbstractBusinessCommand

    %% 组合关系
    DataManager *-- DebugSettingsData
    DataManager *-- BusinessLogicData
    MainWindow *-- DataManager
    MainWindow *-- DebugSettingsDialog
    DebugSettingsDialog *-- DataManager
    DebugSettingsPlugin *-- DataManager
    BusinessLogicPlugin *-- DataManager
    BusinessLogicPlugin *-- BusinessLogicEngine
    BusinessLogicEngine *-- IBusinessCommand

    %% 依赖关系
    DebugSettingsPlugin ..> UTLog : uses
    BusinessLogicPlugin ..> UTLog : uses
    BusinessLogicEngine ..> UTLog : uses
    UTLog ..> UTUtil : depends

    %% 样式定义
    classDef interface fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
    classDef singleton fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef plugin fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef data fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef businessLogic fill:#e3f2fd,stroke:#1565c0,stroke-width:2px
    classDef ui fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef qt fill:#f1f8e9,stroke:#689f38,stroke-width:2px

    class IPlugin interface
    class IBusinessCommand interface
    class UTLog,UTUtil library
    class DebugSettingsPlugin plugin
    class BusinessLogicPlugin plugin
    class DataManager data
    class DebugSettingsData data
    class BusinessLogicData data
    class BusinessLogicEngine businessLogic
    class AbstractBusinessCommand businessLogic
    class MainWindow ui
    class DebugSettingsDialog ui
    class QObject qt
    class QWidget qt
    class QDialog qt
```

#### 设置功能扩展接口

```cpp
// idebugsettingsmodule.h
class IDebugSettingsModule
{
public:
    virtual ~IDebugSettingsModule() {}

    virtual QString moduleName() const = 0;
    virtual QString moduleDescription() const = 0;
    virtual QWidget* createSettingsWidget() = 0;
    virtual void loadSettings() = 0;
    virtual void saveSettings() = 0;
    virtual void resetToDefaults() = 0;
    virtual bool validateSettings() = 0;
};

Q_DECLARE_INTERFACE(IDebugSettingsModule, "com.utsmarttrolley.IDebugSettingsModule")
```

### 构建配置

#### debugsettingsplugin.pro

```pro
TARGET = debugsettingsplugin
TEMPLATE = lib
CONFIG += plugin

QT += core gui widgets

# 版本信息
VERSION = 1.0.0
DEFINES += PLUGIN_VERSION=\\\"$$VERSION\\\"

# 输出目录（使用环境变量）
DESTDIR = $$(UTSTENV)/shlib

# 包含路径（使用环境变量）
INCLUDEPATH += $$(UTSTENV)/interfaces

# 依赖库（使用环境变量）
LIBS += -L$$(UTSTENV)/shlib -lcore -lutils

# 运行时库路径
QMAKE_RPATHDIR += $$(UTSTENV)/shlib

# 源文件
SOURCES += \
    debugsettingsplugin.cpp \
    debugsettingswidget.cpp \
    debugsettingsdialog.cpp \
    switchcabinetsettingsdialog.cpp \
    debugparametersdialog.cpp \
    programupgradedialog.cpp \
    devicelogsdialog.cpp \
    deviceselfcheckdialog.cpp \
    motioncalibrationdialog.cpp \
    debugsettingsdata.cpp

# 头文件
HEADERS += \
    debugsettingsplugin.h \
    debugsettingswidget.h \
    debugsettingsdialog.h \
    switchcabinetsettingsdialog.h \
    debugparametersdialog.h \
    programupgradedialog.h \
    devicelogsdialog.h \
    deviceselfcheckdialog.h \
    motioncalibrationdialog.h \
    debugsettingsdata.h

# 输出目录（插件输出到shlib目录）
DESTDIR = $$(UTSTENV)/shlib
```

#### 主项目集成

```pro
# utsmarttrolley.pro 主项目配置
TEMPLATE = subdirs

# 子项目构建顺序（按依赖关系排序）
SUBDIRS += \
    utils \
    core \
    datacollection \
    plugins/headerareaplugin \
    plugins/videodisplayplugin \
    plugins/distanceinfoplugin \
    plugins/controlareaplugin \
    plugins/debugsettingsplugin \
    plugins/businesslogicplugin \
    main

# 确保依赖关系
core.depends = utils
datacollection.depends = core
plugins/headerareaplugin.depends = core utils
plugins/videodisplayplugin.depends = core utils
plugins/distanceinfoplugin.depends = core utils
plugins/controlareaplugin.depends = core utils
plugins/debugsettingsplugin.depends = core utils
plugins/businesslogicplugin.depends = core utils
main.depends = core datacollection utils
```

#### 核心模块构建配置示例

##### interfaces目录（纯接口定义）
```
interfaces/                        # 无需.pro文件
├── iplugin.h                      # 插件接口定义
├── ibusinesscommand.h             # 业务命令接口
├── idataadapter.h                 # 数据适配器接口
└── commontypes.h                  # 通用类型定义

说明：
- interfaces目录只包含头文件，无需编译
- 通过INCLUDEPATH被其他模块引用
- 保持接口定义的纯净性和稳定性
```

##### core.pro（核心组件库）
```pro
TARGET = core
TEMPLATE = lib
CONFIG += shared

QT += core gui widgets network serialport

# 版本信息
VERSION = 1.0.0

# 输出目录
DESTDIR = $$(UTSTENV)/shlib

# 包含路径
INCLUDEPATH += $$(UTSTENV)/interfaces

# 导出符号定义
DEFINES += CORE_LIBRARY

# 源文件
SOURCES += \
    datamanager.cpp \
    pluginmanager.cpp

# 头文件
HEADERS += \
    core_global.h \
    datamanager.h \
    pluginmanager.h
```

##### datacollection.pro（数据采集库）
```pro
TARGET = datacollection
TEMPLATE = lib
CONFIG += shared

QT += core gui widgets network serialport multimedia

# 版本信息
VERSION = 1.0.0

# 输出目录
DESTDIR = $$(UTSTENV)/shlib

# 包含路径
INCLUDEPATH += $$(UTSTENV)/interfaces

# 依赖库
LIBS += -L$$(UTSTENV)/shlib -lcore

# 导出符号定义
DEFINES += DATACOLLECTION_LIBRARY

# 源文件
SOURCES += \
    abstractdataadapter.cpp \
    datacollectionmanager.cpp \
    sensoradapter.cpp \
    networkapiadapter.cpp

# 头文件
HEADERS += \
    datacollection_global.h \
    abstractdataadapter.h \
    datacollectionmanager.h \
    sensoradapter.h \
    networkapiadapter.h
```

##### utils.pro（工具库）
```pro
TARGET = utils
TEMPLATE = lib
CONFIG += shared

QT += core

# 版本信息
VERSION = 1.0.0

# 输出目录
DESTDIR = $$(UTSTENV)/shlib

# 包含路径
INCLUDEPATH += logging/

# 链接UTLog和UTUtil库
LIBS += -L$$(UTSTENV)/shlib \
        -lutlog \
        -lututil

# 包含UTLog和UTUtil头文件
INCLUDEPATH += \
    $$(UTSTENV)/utils/utlog/include \
    $$(UTSTENV)/utils/ututil/include
```

##### main.pro（主程序）
```pro
TARGET = utsmarttrolley
TEMPLATE = app

QT += core gui widgets

# 版本信息
VERSION = 1.0.0

# 输出目录（可执行程序）
DESTDIR = $$(UTSTENV)/exec

# 包含路径
INCLUDEPATH += $$(UTSTENV)/interfaces

# 依赖库
LIBS += -L$$(UTSTENV)/shlib \
        -lcore \
        -ldatacollection \
        -lutils

# 运行时库路径
QMAKE_RPATHDIR += $$(UTSTENV)/shlib

# 源文件
SOURCES += \
    main.cpp \
    mainwindow.cpp

# 头文件
HEADERS += \
    mainwindow.h
```

##### businesslogicplugin.pro（业务逻辑插件）
```pro
TARGET = businesslogicplugin
TEMPLATE = lib
CONFIG += plugin

QT += core gui widgets

# 版本信息
VERSION = 1.0.0
DEFINES += PLUGIN_VERSION=\\\"$$VERSION\\\"

# 输出目录
DESTDIR = $$(UTSTENV)/shlib

# 包含路径
INCLUDEPATH += $$(UTSTENV)/interfaces \
               engine \
               commands

# 依赖库
LIBS += -L$$(UTSTENV)/shlib -lcore -lutils

# 运行时库路径
QMAKE_RPATHDIR += $$(UTSTENV)/shlib

# 源文件
SOURCES += \
    businesslogicplugin.cpp \
    businesslogicwidget.cpp \
    businesslogicsettingswidget.cpp \
    engine/businesslogicengine.cpp \
    engine/businesslogicdata.cpp \
    engine/abstractbusinesscommand.cpp \
    commands/autoalignmentcommand.cpp \
    commands/pathplanningcommand.cpp \
    commands/devicecalibrationcommand.cpp \
    commands/systemcheckcommand.cpp

# 头文件
HEADERS += \
    businesslogicplugin.h \
    businesslogicwidget.h \
    businesslogicsettingswidget.h \
    engine/businesslogicengine.h \
    engine/businesslogicdata.h \
    engine/abstractbusinesscommand.h \
    commands/autoalignmentcommand.h \
    commands/pathplanningcommand.h \
    commands/devicecalibrationcommand.h \
    commands/systemcheckcommand.h
```

#### 环境配置和部署

##### 开发环境配置
```bash
# 1. 设置环境变量
export UTSTENV=/path/to/your/UtSmartTrolley

# 2. 创建输出目录
mkdir -p $UTSTENV/exec
mkdir -p $UTSTENV/shlib

# 3. 配置库路径（可选，用于运行时）
export LD_LIBRARY_PATH=$UTSTENV/shlib:$LD_LIBRARY_PATH
```

##### 构建流程
```bash
# 1. 进入项目根目录
cd $UTSTENV

# 2. 生成Makefile
qmake utsmarttrolley.pro

# 3. 编译项目
make -j$(nproc)

# 4. 验证输出
ls -la $UTSTENV/exec/     # 查看可执行文件
ls -la $UTSTENV/shlib/    # 查看库文件和插件
```

##### Ubuntu系统依赖
```bash
# 安装Qt 4.8.6开发环境
sudo apt-get update
sudo apt-get install qt4-dev-tools libqt4-dev

# 安装必要的系统库
sudo apt-get install build-essential
sudo apt-get install libqt4-network libqt4-serialport

# 验证Qt版本
qmake --version
```

##### 运行时配置
```bash
# 1. 确保库路径正确
export LD_LIBRARY_PATH=$UTSTENV/shlib:$LD_LIBRARY_PATH

# 2. 运行主程序
cd $UTSTENV/exec
./utsmarttrolley

# 3. 插件加载验证
# 程序启动时会自动从 $UTSTENV/shlib/ 目录加载插件
```


## 总结

本设计文档详细描述了智能运载小车控制端界面系统的架构设计，采用"接口与实现分离"的现代化架构理念，包括：

### 核心架构特性

1. **接口与实现分离**：
   - `interfaces/` 目录包含纯接口定义，保持API稳定性
   - 具体实现按功能域组织为独立的库模块
   - 清晰的依赖关系和模块边界

2. **模块化库设计**：
   - `core/` - 核心组件库（DataManager、PluginManager）
   - `datacollection/` - 数据采集库（统一数据源接入）
   - `utils/` - 工具库（日志记录等基础功能）
   - `businesslogicplugin/` - 业务逻辑插件（包含完整的命令执行框架）

3. **插件化架构**：
   - 简化的IPlugin接口，专注核心功能
   - 支持功能模块的独立开发和动态加载
   - 统一的插件管理和生命周期控制

4. **统一构建系统**：
   - 基于qmake的模块化构建配置
   - 使用$$(UTSTENV)环境变量统一路径管理
   - 清晰的依赖关系和构建顺序

### 技术优势

- **高度模块化**：每个功能域独立开发、测试、部署
- **易于维护**：接口稳定，实现可独立迭代
- **团队协作**：支持并行开发，减少代码冲突
- **系统扩展**：新功能可通过新增库或插件实现

### 应用价值

该架构设计为智能运载小车控制端界面系统提供了：
- 稳定可靠的插件化框架
- 灵活可扩展的数据采集能力
- 统一规范的业务逻辑执行机制
- 完整的调试和配置管理功能

通过这种现代化的架构设计，系统具备了良好的可维护性、可扩展性和可测试性，为智能运载小车项目的长期发展奠定了坚实的技术基础。




