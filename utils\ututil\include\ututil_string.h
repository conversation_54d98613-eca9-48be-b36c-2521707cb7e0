﻿#ifndef __STRINGUTIL_H
#define __STRINGUTIL_H
#include <iostream>
#include <string>
#include <stdarg.h>
#include <string.h>
#include <time.h>
#include <vector>
#include <map>
#include <unordered_map>
#include <sstream>
#include "ututil_tools.h"


DEF_BEG_UTUTIL

using namespace std;

class UTUTIL CStringUtil
{
public:
    
    /**
     * @brief 根据分隔符把字节数组组合成16进制的字符串;如：字节数组 -> "68 00 04 00 08"， 这里分隔符sep是空格
     * @param data 字节数组
     * @param sep 分隔符
     * @return 16进制的字符串
     */
    static std::string toHexString(const std::string &data, const std::string &sep);


    /**
     * @brief 将带格式的字符串转换成std::string
     * @param msgFormat 字符串格式
     * @return std::string字符串
     */
    static std::string toString(const char* msgFormat, ...);
    
    /**
     * @brief 将带格式的字符串转换成std::wstring
     * @param msgFormat 字符串格式
     * @return std::wstring
     */
    static std::wstring toString(const WCHAR *format, ...);

    /**
     * 固定宽度填充字符串, 用于输出对齐格式用(默认右填充)
     * @param s 原始字符串
     * @param pad 填充字符，默认为 空格
     * @param n 填充位数
     * @param rightPad 是否右填充，默认是
     * @return 填充字符串
     */
    static std::string outfill(const std::string& s, char pad = ' ', size_t n = 50, bool rightPad=true)
    {
        if(n <= s.length())
            return s;

        if(rightPad)
            return (s + std::string((n - s.length()), pad));

        return (std::string((n - s.length()), pad) + s);
    }

    /**
    * @brief  去掉头部以及尾部的字符或字符串.
    *
    * @param sStr    输入字符串
    * @param s       需要去掉的字符，默认为 空格，换行，TAB
    * @param bChar   如果为true, 则去掉s中每个字符; 如果为false, 则去掉s字符串
    * @return string 返回去掉后的字符串
    */
    static std::string trim(const std::string &sStr, const std::string &s = " \r\n\t", bool bChar = true);

    /**
    * @brief  去掉左边的字符或字符串.
    *
    * @param sStr    输入字符串
    * @param s       需要去掉的字符
    * @param bChar   如果为true, 则去掉s中每个字符; 如果为false, 则去掉s字符串
    * @return string 返回去掉后的字符串
    */
    static std::string trimleft(const std::string &sStr, const std::string &s = " \r\n\t", bool bChar = true);

    /**
    * @brief  去掉右边的字符或字符串.
    *
    * @param sStr    输入字符串
    * @param s       需要去掉的字符
    * @param bChar   如果为true, 则去掉s中每个字符; 如果为false, 则去掉s字符串
    * @return string 返回去掉后的字符串
    */
    static std::string trimright(const std::string &sStr, const std::string &s = " \r\n\t", bool bChar = true);

    /**
    * @brief  字符串转换成小写.
    *
    * @param sString  字符串
    * @return string  转换后的字符串
    */
    static std::string lower(const std::string &sString);

    /**
    * @brief  字符串转换成大写.
    *
    * @param sString  字符串
    * @return string  转换后的大写的字符串
    */
    static std::string upper(const std::string &sString);

    /**
    * @brief  字符串是否都是数字的.
    *
    * @param sString  字符串
    * @return bool    是否是数字
    */
    static bool isdigit(const std::string &sInput);

    /*
    * 判断字符串是否以某个字符串开头
    * 参数：
    *	str -- 待判断字符串
    *	substr -- 开头字符串
    * 返回：
    *	bool 返回比较结果 true表示是以substr开头，false表示不是以substr开头
    */
    static bool startsWith(const std::string &str, const std::string &substr);

    /*
    * 判断字符串是否以某个字符串结尾
    * 参数：
    *	str -- 待判断字符串
    *	substr -- 结尾字符串
    * 返回：
    *	bool 返回比较结果 true表示是以substr结尾，false表示不是以substr结尾
    */
    static bool endsWith(const std::string &str, const std::string &substr);

    /*
    * 不区分大小写字符串比较
    * 参数：
    *	str1 -- 第一个字符串
    *	str2 -- 第二个字符串
    * 返回：
    *	bool 返回比较结果 true表示相等，false表示不相等
    */
    static bool equalsIgnoreCase(const std::string &str1, const std::string &str2);

    /**
    * @brief  字符串转化成T型，如果T是数值类型, 如果str为空,则T为0.
    *
    * @param sStr  要转换的字符串
    * @return T    T型类型
    */
    template<typename T>
    static T strto(const std::string &sStr);

    /**
    * @brief  字符串转化成T型.
    *
    * @param sStr      要转换的字符串
    * @param sDefault  缺省值
    * @return T        转换后的T类型
    */
    template<typename T>
    static T strto(const std::string &sStr, const std::string &sDefault);

    /**
    * @brief  解析字符串,用分隔符号分隔,保存在vector里
    *
    * 例子: |a|b||c|
    *
    * 如果withEmpty=true时, 采用|分隔为:"","a", "b", "", "c", ""
    *
    * 如果withEmpty=false时, 采用|分隔为:"a", "b", "c"
    *
    * 如果T类型为int等数值类型, 则分隔的字符串为"", 则强制转化为0
    *
    * @param sStr      输入字符串
    * @param sSep      分隔字符串(每个字符都算为分隔符)
    * @param withEmpty true代表空的也算一个元素, false时空的过滤
    * @return          解析后的字符vector
    */
    template<typename T>
    static std::vector<T> sepstr(const std::string &sStr, const std::string &sSep, bool withEmpty = false);

    /**
    * @brief T型转换成字符串，只要T能够使用ostream对象用<<重载,即可以被该函数支持
    * @param t 要转换的数据
    * @return  转换后的字符串
    */
    template<typename T>
    inline static std::string tostr(const T &t)
    {
        std::ostringstream sBuffer;
        sBuffer << t;
        return sBuffer.str();
    }

    /**
     * @brief  vector转换成string.
     *
     * @param t 要转换的vector型的数据
     * @param sSep 分隔符，默认为空格
     * @return  转换后的字符串
     */
    template<typename T>
    static std::string tostr(const std::vector<T> &t, const std::string &sSep="");

    /**
     * @brief  把map输出为字符串.
     *
     * @param map<K, V, D, A>  要转换的map对象
     * @return                    string 输出的字符串
     */
    template<typename K, typename V, typename D, typename A>
    static std::string tostr(const std::map<K, V, D, A> &t);

    /**
     * @brief  map输出为字符串.
     *
     * @param multimap<K, V, D, A>  map对象
     * @return                      输出的字符串
     */
    template<typename K, typename V, typename D, typename A>
    static std::string tostr(const std::multimap<K, V, D, A> &t);

    /**
     * @brief  把map输出为字符串.
     *
     * @param map<K, V, D, A>  要转换的map对象
     * @return                    string 输出的字符串
     */
    template<typename K, typename V, typename D, typename P, typename A>
    static std::string tostr(const std::unordered_map<K, V, D, P, A> &t);

    /**
    * @brief  pair 转化为字符串，保证map等关系容器可以直接用tostr来输出
    * @param pair<F, S> pair对象
    * @return           输出的字符串
    */
    template<typename F, typename S>
    static std::string tostr(const std::pair<F, S> &itPair);

    /**
    * @brief  container 转换成字符串.
    *
    * @param iFirst  迭代器
    * @param iLast   迭代器
    * @param sSep    两个元素之间的分隔符
    * @return        转换后的字符串
    */
    template<typename InputIter>
    static std::string tostr(InputIter iFirst, InputIter iLast, const std::string &sSep = "|");



    /**
    * @brief  二进制数据转换成字符串.
    *
    * @param buf     二进制buffer
    * @param len     buffer长度
    * @param sSep    分隔符
    * @param lines   多少个字节换一行, 默认0表示不换行
    * @return        转换后的字符串
    */
    static std::string bin2str(const void *buf, size_t len, const std::string &sSep = "", size_t lines = 0);

    /**
    * @brief  二进制数据转换成字符串.
    *
    * @param sBinData  二进制数据
    * @param sSep     分隔符
    * @param lines    多少个字节换一行, 默认0表示不换行
    * @return         转换后的字符串
    */
    static std::string bin2str(const std::string &sBinData, const std::string &sSep = "", size_t lines = 0);

    /**
    * @brief   字符串转换成二进制.
    *
    * @param psAsciiData 字符串
    * @param sBinData    二进制数据
    * @param iBinSize    需要转换的字符串长度
    * @return            转换长度，小于等于0则表示失败
    */
    static int str2bin(const char *psAsciiData, unsigned char *sBinData, int iBinSize);

    /**
     * @brief  字符串转换成二进制.
     *
     * @param sBinData  要转换的字符串
     * @param sSep      分隔符
     * @param lines     多少个字节换一行, 默认0表示不换行
     * @return          转换后的二进制数据
     */
    static std::string str2bin(const std::string &sBinData, const std::string &sSep = "", size_t lines = 0);

    /**
    * @brief  替换字符串.
    *
    * @param sString  输入字符串
    * @param sSrc     原字符串
    * @param sDest    目的字符串
    * @return string  替换后的字符串
    */
    static std::string replace(const std::string &sString, const std::string &sSrc, const std::string &sDest);

    /**
    * @brief  批量替换字符串.
    *
    * @param sString  输入字符串
    * @param mSrcDest  map<原字符串,目的字符串>
    * @return string  替换后的字符串
    */
    static std::string replace(const std::string &sString, const std::map<std::string, std::string>& mSrcDest);

    /**
     * @brief 匹配以.分隔的字符串，pat中*则代表通配符，代表非空的任何字符串
     * s为空, 返回false ，pat为空, 返回true
     * @param s    普通字符串
     * @param pat  带*则被匹配的字符串，用来匹配ip地址
     * @return     是否匹配成功
     */
    static bool matchPeriod(const std::string& s, const std::string& pat);

     /**
     * @brief  匹配以.分隔的字符串.
     *
     * @param s   普通字符串
     * @param pat vector中的每个元素都是带*则被匹配的字符串，用来匹配ip地址
     * @return    是否匹配成功
     */
    static bool matchPeriod(const std::string& s, const std::vector<std::string>& pat);

    
    /**
     * @name 编码转换函数
     * @{
     */
    //! unicode转换成gbk编码
    static std::string unicodeToGbk(const std::wstring& str);

    //! gbk转换成unicode编码
    static std::wstring gbkToUnicode(const std::string& str);

    //! utf8转换成unicode编码
    static std::wstring utf8ToUnicode(const std::string& str);

    //! unicode转换成utf8编码
    static std::string unicodeToUtf8(const std::wstring& str);

    //! utf8 转换成gbk编码
    static std::string utf8ToGbk(const std::string& str);

    //! gbk转换成utf8编码
    static std::string gbkToUtf8(const std::string& str);
    /** @} */
    
    /**
     * @name 格式化字符串,参见标准函数:
     * @{
     * @param bufSize 设置返回字符串缓存最大值
     * @param mat 格式化字符串
     * @return 返回字符串类
     * @see snprintf
     */
    static std::string format(const char* mat, ...);
    static std::wstring format(const WCHAR* mat, ...);
    static std::string format_s(size_t bufSize, const char* mat, ...);
    static std::wstring  format_s(size_t bufSize, const WCHAR* mat, ...);
    
    //! @see vsnprintf
	static inline size_t sprintf_ex(char *str, size_t size, const char *format, va_list& params)
	{
#ifdef LINUX
		return vsnprintf(str, size, format, params);
#else
		return _vsnprintf(str, size, format, params);
#endif
	}

	//! @see vswprintf
	static inline size_t sprintf_ex(WCHAR *str, size_t size, const WCHAR *format, va_list& params)
	{
#ifdef LINUX
		return vswprintf(str, size, format, params);
#else
		return _vsnwprintf(str, size, format, params);
#endif
	}

    
    template<class T>
    static typename std::basic_string<T> inline_format(const T* mat, va_list& params)
    {
        std::basic_string<T> str;
        if (!mat) return str;
        str.resize(1024);
        size_t rcvSize = 0;
        do
        {
            // 格式化
            rcvSize = sprintf_ex((T*)str.data(), str.size(), mat, params);
            // 若缓存不够则以2倍的速度增长
            if (rcvSize > str.size())
            {
                str.resize(str.size() * 2);
                continue;
            }
            break;
        } while (true);
        str.erase(str.begin() + rcvSize, str.end());
        return STD_MOVE(str);
    }

    template<class T>
    static typename std::basic_string<T> inline_format_s(size_t bufSize, const T* mat, va_list& params)
    {
        if (!mat || bufSize <= 0)
        {
            return 0;
        }
        std::basic_string<T> str;

        // 调整内存
        str.resize(bufSize);

        size_t rcvSize = sprintf_ex((T*)str.data(), str.size(), mat, params);
        if (rcvSize <= str.size()) {
            str.erase(str.begin() + rcvSize, str.end());
        } else{
            str.clear();
        }
        return STD_MOVE(str);
    }    
};

namespace p
{
    template<typename D>
    struct strto1
    {
        D operator()(const std::string &sStr)
        {
            std::string s = "0";

            if(!sStr.empty())
            {
                s = sStr;
            }

            std::istringstream sBuffer(s);

            D t;
            sBuffer >> t;

            return t;
        }
    };

    template<>
    struct strto1<char>
    {
        char operator()(const std::string &sStr)
        {
            if(!sStr.empty())
            {
                return sStr[0];
            }
            return 0;
        }
    };

    template<>
    struct strto1<unsigned char>
    {
        unsigned char operator()(const std::string &sStr)
        {
            if(!sStr.empty())
            {
                return (unsigned char)sStr[0];
            }
            return 0;
        }
    };

    template<>
    struct strto1<short>
    {
        short operator()(const std::string &sStr)
        {
            if (!sStr.empty()) {
                if (sStr.find("0x") == 0) {
                    return (short) ::strtol(sStr.c_str(), NULL, 16);
                }
                else {
                    return atoi(sStr.c_str());
                }
            }
            return 0;
        }
    };

    template<>
    struct strto1<unsigned short>
    {
        unsigned short operator()(const std::string &sStr)
        {
            if (!sStr.empty()) {
                if (sStr.find("0x") == 0) {
                    return (unsigned short) ::strtoul(sStr.c_str(), NULL, 16);
                }
                else {
                    return (unsigned short) strtoul(sStr.c_str(), NULL, 10);
                }
            }
            return 0;
        }
    };

    template<>
    struct strto1<int>
    {
        int operator()(const std::string &sStr)
        {
            if (!sStr.empty()) {
                if (sStr.find("0x") == 0) {
                    return ::strtol(sStr.c_str(), NULL, 16);
                }
                else {
                    return atoi(sStr.c_str());
                }
            }
            return 0;
        }
    };

    template<>
    struct strto1<unsigned int>
    {
        unsigned int operator()(const std::string &sStr)
        {
            if (!sStr.empty()) {
                if (sStr.find("0x") == 0) {
                    return ::strtoul(sStr.c_str(), NULL, 16);
                }
                else {
                    return strtoul(sStr.c_str(), NULL, 10);
                }
            }
            return 0;
        }
    };

    template<>
    struct strto1<long>
    {
        long operator()(const std::string &sStr)
        {
            if (!sStr.empty()) {
                if (sStr.find("0x") == 0) {
                    return ::strtol(sStr.c_str(), NULL, 16);
                }
                else {
                    return atol(sStr.c_str());
                }
            }
            return 0;
        }
    };

    template<>
    struct strto1<long long>
    {
        long long operator()(const std::string &sStr)
        {
            if (!sStr.empty()) {
                if (sStr.find("0x") == 0) {
                    return ::strtoll(sStr.c_str(), NULL, 16);
                }
                else {
                    return atoll(sStr.c_str());
                }
            }
            return 0;
        }
    };

    template<>
    struct strto1<unsigned long>
    {
        unsigned long operator()(const std::string &sStr)
        {
            if (!sStr.empty()) {
                if (sStr.find("0x") == 0) {
                    return ::strtoul(sStr.c_str(), NULL, 16);
                }
                else {
                    return strtoul(sStr.c_str(), NULL, 10);
                }
            }
            return 0;
        }
    };

    template<>
    struct strto1<float>
    {
        float operator()(const std::string &sStr)
        {
            if(!sStr.empty())
            {
                return (float) atof(sStr.c_str());
            }
            return 0;
        }
    };

    template<>
    struct strto1<double>
    {
        double operator()(const std::string &sStr)
        {
            if(!sStr.empty())
            {
                return atof(sStr.c_str());
            }
            return 0;
        }
    };

    template<typename D>
    struct strto2
    {
        D operator()(const std::string &sStr)
        {
            std::istringstream sBuffer(sStr);

            D t;
            sBuffer >> t;

            return t;
        }
    };

    template<>
    struct strto2<std::string>
    {
        std::string operator()(const std::string &sStr)
        {
            return sStr;
        }
    };

}

template<>
std::string CStringUtil::tostr<bool>(const bool &t);

template<>
std::string CStringUtil::tostr<char>(const char &t);

template<>
std::string CStringUtil::tostr<unsigned char>(const unsigned char &t);

template<>
std::string CStringUtil::tostr<short>(const short &t);

template<>
std::string CStringUtil::tostr<unsigned short>(const unsigned short &t);

template<>
std::string CStringUtil::tostr<int>(const int &t);

template<>
std::string CStringUtil::tostr<unsigned int>(const unsigned int &t);

template<>
std::string CStringUtil::tostr<long>(const long &t);

template<>
std::string CStringUtil::tostr<long long>(const long long &t);

template<>
std::string CStringUtil::tostr<unsigned long>(const unsigned long &t);

template<>
std::string CStringUtil::tostr<float>(const float &t);

template<>
std::string CStringUtil::tostr<double>(const double &t);

template<>
std::string CStringUtil::tostr<long double>(const long double &t);

template<>
std::string CStringUtil::tostr<std::string>(const std::string &t);


template<typename T>
T CStringUtil::strto(const std::string &sStr)
{
    using strto_type = typename std::conditional<std::is_arithmetic<T>::value, p::strto1<T>, p::strto2<T>>::type;

    return strto_type()(sStr);
}

template<typename T>
T CStringUtil::strto(const std::string &sStr, const std::string &sDefault)
{
    std::string s;

    if(!sStr.empty())
    {
        s = sStr;
    }
    else
    {
        s = sDefault;
    }

    return strto<T>(s);
}


template<typename T>
std::vector<T> CStringUtil::sepstr(const std::string &sStr, const std::string &sSep, bool withEmpty)
{
    std::vector<T> vt;
    std::string::size_type pos = 0;
    std::string::size_type pos1 = 0;

    while (true) {
        std::string s;
        pos1 = sStr.find_first_of(sSep, pos);
        if (pos1 == std::string::npos) {
            if (pos + 1 <= sStr.length()) {
                s = sStr.substr(pos);
            }
        }
        else if (pos1 == pos) {
            s = "";
        }
        else {
            s = sStr.substr(pos, pos1 - pos);
            pos = pos1;
        }

        if (withEmpty) {
            vt.push_back(std::move(strto<T>(s)));
        }
        else {
            if (!s.empty()) {
                vt.push_back(std::move(strto<T>(s)));
            }
        }

        if (pos1 == std::string::npos) {
            break;
        }
        pos++;
    }
    return vt;
}

template<typename T>
std::string CStringUtil::tostr(const std::vector<T> &t, const std::string &sSep)
{
    std::string s;
    for(size_t i = 0; i < t.size(); i++)
    {
        s += tostr(t[i]);
        s += sSep;
    }
    return s;
}

template<typename K, typename V, typename D, typename A>
std::string CStringUtil::tostr(const std::map<K, V, D, A> &t)
{
    std::string sBuffer;
    typename std::map<K, V, D, A>::const_iterator it = t.begin();
    while(it != t.end())
    {
        sBuffer += " [";
        sBuffer += tostr(it->first);
        sBuffer += "]=[";
        sBuffer += tostr(it->second);
        sBuffer += "] ";
        ++it;
    }
    return sBuffer;
}

template<typename K, typename V, typename D, typename A>
std::string CStringUtil::tostr(const std::multimap<K, V, D, A> &t)
{
    std::string sBuffer;
    typename std::multimap<K, V, D, A>::const_iterator it = t.begin();
    while(it != t.end())
    {
        sBuffer += " [";
        sBuffer += tostr(it->first);
        sBuffer += "]=[";
        sBuffer += tostr(it->second);
        sBuffer += "] ";
        ++it;
    }
    return sBuffer;
}

template<typename K, typename V, typename D, typename P, typename A>
std::string CStringUtil::tostr(const std::unordered_map<K, V, D, P, A> &t)
{
    std::string sBuffer;
    typename std::unordered_map<K, V, D, P, A>::const_iterator it = t.begin();
    while (it != t.end()) {
        sBuffer += " [";
        sBuffer += tostr(it->first);
        sBuffer += "]=[";
        sBuffer += tostr(it->second);
        sBuffer += "] ";
        ++it;
    }
    return sBuffer;
}

template<typename F, typename S>
std::string CStringUtil::tostr(const pair<F, S> &itPair)
{
    std::string sBuffer;
    sBuffer += "[";
    sBuffer += tostr(itPair.first);
    sBuffer += "]=[";
    sBuffer += tostr(itPair.second);
    sBuffer += "]";
    return sBuffer;
}

template<typename InputIter>
std::string CStringUtil::tostr(InputIter iFirst, InputIter iLast, const std::string &sSep)
{
    std::string sBuffer;
    InputIter it = iFirst;

    while(it != iLast)
    {
        sBuffer += tostr(*it);
        ++it;

        if(it != iLast)
        {
            sBuffer += sSep;
        }
        else
        {
            break;
        }
    }

    return sBuffer;
}

DEF_END_UTUTIL

#endif // CStringUtil_H
