﻿#ifndef __UTUTIL_QALGORITHM_H__
#define __UTUTIL_QALGORITHM_H__
#include "ututil_tools.h"

DEF_BEG_UTUTIL

//! 返回两者较小者
template <class T> 
inline const T& qmin (const T& a, const T& b) {
	return !(b<a)?a:b;     // or: return !comp(b,a)?a:b; for version (2)
}

//! 返回两者较大者
template <class T>
inline const T& qmax(const T& a, const T& b)
{
	return (a>b) ? a : b;
}

/**
 * @brief 只有当条件为真时才拷贝
 * @param _First 源开始位置
 * @param _Last 源结束位置
 * @param _Dest 目标开始位置
 * @param _Pred 判断函数
 * @return 返回目标的末尾位置
 */ 
template<class _InIt,
		 class _OutIt,
		 class _Pr> inline
_OutIt copy_real(_InIt _First, _InIt _Last, _OutIt _Dest, _Pr _Pred)
{
	for (; _First != _Last; ++_First){
		if (_Pred(*_First))
			*_Dest++ = *_First;
		else{
			break;
		}
	}
	return (_Dest);
}

/** 
 * @brief 拷贝序列2中的值到序列1中
 * @param v1 序列1
 * @param s1 序列1中的容量
 * @param v2 序列2
 * @param s2 序列2待拷贝的数量
 * @return 返回拷贝成功的大小
*/
template<class Value1,
		 class Value2>
size_t copy_seq(Value1* v1, size_t s1, const Value2* v2, size_t s2)
{
	s1 = qmin(s1, s2);
	size_t s = 0;
	for (s = 0; s != s1; ++s) *v1++ = (Value1)(*v2++);

	return s;
}

/**
 * @brief 拷贝n个数量的内容到dest中
 * @param first 源内容的首位值
 * @param count 需要拷贝的大小
 * @param dest 接收内容的缓冲区
 * @return 返回目标缓冲区的首位值
 * @remarks 该函数只有在高版本的c++stl中<algorithm>才有故而在这里加入进来
 */ 
template<class InputIterator, class Size, class OutputIterator>  
OutputIterator copy_n(InputIterator first, Size count, OutputIterator dest)
{
	OutputIterator result = dest;
	for ( ; count > 0; --count) {
		*result = *first;
		++first;
		++result;
	}
	return dest;
}


/**
 * @brief 比较两个序列
 * @param first1 序列1的开始
 * @param count1 序列1的长度
 * @param first2 序列2的开始
 * @param count2 序列2的长度
 * @return 若序列1比序列2大则返回1，等于则返回0，小于则返回-1
 * @remark 可以参考strcmp的用法，其算法与strcmp类似
*/
template <class Iterator, class Size>
int Compare(Iterator first1, Size count1, Iterator first2, Size count2)
{
	int count = qmin(count1, count2);
	for(int i = 0; i < count; i++)
	{
		if(*first1 < *first2) return -1;
		else if(*first2 < *first1) return 1;

		++first1;
		++first2;
	}
	if(count1 > count2) return 1;
	else if(count1 < count2) return -1;

	return 0;
}

/** 
 * @brief 计算一个序列的大小，当遇到结束符、或者到达end位置则停止计算
 * @param first 序列开始位置
 * @param end 序列结束位置
 * @param EndValue 结束符
 * @return 返回序列大小
*/
template<class Iterator, class ValueType>
size_t count_size(Iterator first, Iterator end, const ValueType& EndValue)
{
	size_t size = 0;
	while (first != end)
	{
		if (*first == EndValue) break;
		++size;
		++first;
	}

	return size;
}

/**
 * @brief 计算一个序列的大小，当遇到结束符时则停止计算
 * @param first 序列开始位置
 * @param EndValue 结束符
 * @return 返回序列大小
*/
template<class Iterator, class ValueType>
size_t count_size(Iterator first, const ValueType& EndValue)
{
	size_t size = 0;
	while (*first != EndValue)
	{
		++size;
		++first;
	}

	return size;
}

/**
 * @brief 逆序查找
 * @param first 序列首
 * @param last 序列尾
 * @param val 查找的值
 * @return 返回逆序查找首次出现的位置,失败返回last迭代器位置
 */
template<class Iterator, class ValueType>
Iterator find_last_of(Iterator first, Iterator last, ValueType val)
{
	Iterator end = last;
	--last;
	for (; last != first; --last)
	{
		if (*last == val) break;
	}
	if (*last == val) return last;

	return end;
}

/**
 * @brief 查找第一个不为val的字符所在位置
 * @param first 序列首
 * @param last 序列尾
 * @param val 查找的值
 * @return 返回序列第一次不为val的位置，若不存在返回last迭代器
 */
template<class Iterator, class ValueType>
Iterator find_first_not_of(Iterator first, Iterator last, ValueType val)
{
	for (; first != last; ++first)
	{
		if (*first != val) return first;
	}

	return last;
}

/**
 * @brief 查找第一个为val的字符所在位置
 * @param first 序列首
 * @param last 序列尾
 * @param val 查找的值
 * @return 返回序列第一次不为val的位置，若不存在返回last迭代器
 */
template<class Iterator, class ValueType>
Iterator find_first_of(Iterator first, Iterator last, ValueType val)
{
	for (; first != last; ++first)
	{
		if (*first == val) return first;
	}

	return last;
}

/**
 * @brief 逆向查找第一个不为val的字符所在位置
 * @param first 序列首
 * @param last 序列尾
 * @param val 查找的值
 * @return 返回逆序列第一次不为val的位置，若不存在返回last迭代器
 */
template<class Iterator, class ValueType>
Iterator find_last_not_of(Iterator first, Iterator last, ValueType val)
{
	Iterator end = last;
	--last;
	for (; last != first; --last)
	{
		if (*last != val) break;
	}
	if (*last != val) return last;

	return end;
}

/**
 * @brief 从序列容器中删除一个元素
 * @param c 支持前向访问，和迭代器删除的容器
 * @param val 待删除的值
*/
template<class Cont, class Value>
typename Cont::iterator seq_erase(Cont& c, const Value& val)
{
	typename Cont::iterator it = c.begin();
	for (; it != c.end(); ++it)
	{
		if (*it == val) c.erase(it);
	}

	return it;
}

/**
 * @brief 比较两个序列
 * @param f1 序列1开始位置
 * @param l1 序列1结束位置
 * @param f2 序列2开始位置
 * @param l2 序列2结束位置
 */
template<class Itea1, class Itea2>
bool equal(Itea1 f1, Itea1 l1, Itea2 f2, Itea2 l2)
{
	for (; (f1 != l1) && (f2 != l2); ++f1, ++f2) {
		if (!(*f1 == *f2)) {
			return false;
		}
	}
	if(f1 != l1 || f2 != l2) return false;
	return true;
}

DEF_END_UTUTIL

#endif
