﻿#include "ututil_shareapi.h"
#include "ututil_net.h"
#ifdef LINUX
#include <net/if.h>       /* for ifconf */
#include <linux/sockios.h>    /* for net status mask */
#include <netinet/in.h>       /* for sockaddr_in */
#include <sys/socket.h>
#include <sys/types.h>
#include <sys/ioctl.h>
#include <arpa/inet.h>
#endif

USE_UTUTIL;

/**
 * @brief 获取本机IP地址
 * @return 本机IP地址
 */
std::string CNetUtil::getLocalIp()
{
#ifdef LINUX
    int i=0;
    int sockfd;
    struct ifconf ifconf;
    char buf[512];
    struct ifreq *ifreq;
    char* ip;
    //初始化ifconf
    ifconf.ifc_len = 512;
    ifconf.ifc_buf = buf;

    if((sockfd = socket(AF_INET, SOCK_DGRAM, 0))<0)
    {
        return "";
    }
    ioctl(sockfd, SIOCGIFCONF, &ifconf);    //获取所有接口信息
    close(sockfd);

    //接下来一个一个的获取IP地址
    ifreq = (struct ifreq*)buf;
    for(i=(ifconf.ifc_len/sizeof(struct ifreq)); i>0; i--)
    {
        ip = inet_ntoa(((struct sockaddr_in*)&(ifreq->ifr_addr))->sin_addr);
        //std::cout << ip << std::endl;
        if(strcmp(ip,"127.0.0.1")==0)  //排除127.0.0.1，继续下一个
        {
            ifreq++;
            continue;
        }
        return ip;
    }
#endif
    return "";
}

