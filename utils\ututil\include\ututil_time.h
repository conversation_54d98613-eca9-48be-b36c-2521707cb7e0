﻿#ifndef __UTUTIL_TIME_H__
#define __UTUTIL_TIME_H__
#include "ututil_sharedptr.h"
#include <ctime>
#include <string>

DEF_BEG_UTUTIL

//typedef UINT64 TIME_T;

/**
 * @brief 时间结构体
 */
typedef struct _DateTime_
{
	short nYear;		///< 年
	short nMonth;		///< 月
	short nDay;			///< 日
	short nHour;		///< 时
	short nMinute;		///< 分
	short nSecond;		///< 秒
	short nMillisecond;	///< 毫秒
	short nReserved;	///< 字节对齐保留
}DateTime,*PDateTime;

/**
 * @brief 时间工具类
 */
class UTUTIL CTimeUtil
{
public:
    
	/** 
	 * @brief 获取当前日期时间字符串精确到毫秒(yyyy-mm-dd hh:mm:ss.mmm),如：2021-04-19 14:12:30.154
	 * @param chDate 日期之间的分割符
	 * @param chBoth 日期和时间之间的分割符
	 * @param chTime 时间之间的分割符
	 * @param chMill 时间与毫秒之间的分割符
	 * @return 返回格式化后的字符串
	 * @remarks 如果某个参数传入为0，则不使用分割符(yyyy-mm-dd hh:mm:ss.mmm)
	 */
	static std::string getDateTimeStr(int chDate = '-', int chBoth = ' ', int chTime = ':', int chMill = '.');

	/** 
	 * @brief 获取当前日期字符串(年-月-日)如：2021-04-19
	 * @param chDate 日期之间的分割符
	 * @return 返回格式化后的字符串日期
	 * @remarks 如果某个参数传入为0，则不使用分割符(yyyy-mm-dd)
	*/
	static std::string getDateStr(int chDate = '-');

	/** 
	 * @brief 获取当前时间字符串(时:分:秒.毫秒)
	 * @param chTime 时间之间的分割符
	 * @param chMill 时间与毫秒之间的分割符
	 * @return 返回格式化后的字符串时间
	 * @remarks 如果某个参数传入为0，则不使用分割符(hh-mm-ss.mmm)
	*/
	static std::string getTimeStr(int chTime = ':', int chMill = '.');

	//! 获取当前时间信息
	static tm* getCTimeInfo(tm* tinfo);

	//! 获取当前时间信息
	static PDateTime getTimeInfo(PDateTime tinfo);

	/**
	 * @brief 从当前或者传入的时间值中获取当前日期值(年月日),如：20190523
	 * @param t 传入的当前时间值 (time函数获取)
	 * @return 返回日期值
	 * @remarks 若t为null，则函数自动获取当前的时间
	 */
	static long getDateValue(time_t *t);
    
    /**
     * @brief 获取当前日期时间数值(年月日),如：20190523
     * @return 返回日期值
     * @remarks 若t为null，则函数自动获取当前的时间
     */
    static long getCurDateValue();
    
    /**
     * @brief 获取当前日期时间数值(年月日时分秒),如：20190523161230
     * @return 返回日期值 
     * @remarks 若t为null，则函数自动获取当前的时间
     */
    static UINT64 getCurDateTimeValue();
    
    /**
     * @brief 获取当前日期时间数值(年月日时分秒毫秒),20190523161230230
     * @return 返回日期值
     * @remarks 若t为null，则函数自动获取当前的时间
     */
    static UINT64 getCurDateTimeMsValue();
};

DEF_END_UTUTIL

#endif // __TIME_H__
