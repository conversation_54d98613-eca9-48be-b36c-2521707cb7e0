# UTLog - 通用日志库

## 项目概述

UTLog 是一个基于 spdlog 的 C++ 日志库，提供了简单易用的日志记录功能。该库支持多种日志级别、文件轮转、控制台输出以及代码位置跟踪等功能。

## 特性

- 🚀 **高性能**: 基于 spdlog 实现，支持异步日志记录
- 📝 **多级别日志**: 支持 trace、debug、info、warn、error、critical 六个级别
- 🔄 **文件切分**: 默认按“天”切分日志文件，并可配置保留天数；亦支持按大小滚动
- 🖥️ **控制台输出**: 支持彩色控制台输出
- 📍 **位置跟踪**: 可选的源代码位置跟踪（文件名、行号、函数名）
- 🔧 **灵活配置**: 支持运行时配置日志级别、输出方式等
- 🎯 **宏支持**: 提供便捷的日志记录宏

## 项目结构

```
utlog/
├── include/
│   └── utlog.h          # 主要头文件，包含所有公共接口
├── src/
│   ├── utlog.cpp        # 主要实现文件
│   └── utlogger.h       # 内部日志器实现
├── third/
│   └── spdlog/          # spdlog 第三方库
└── utlog.pro            # qmake 项目文件
```

## 编译要求

- **编译器**: 支持 C++11 的编译器
- **依赖库**: 
  - spdlog (已包含在 third 目录)
  - ututil (工具库依赖)
- **平台**: Windows / Linux
- **构建工具**: qmake

## 编译方法

```bash
# 设置环境变量 UTSTENV 指向项目根目录
export UTSTENV=/path/to/your/project

# 使用 qmake 编译
qmake utlog.pro
make
```

编译后会在 `$UTSTENV/shlib` 目录下生成动态库文件。

## 快速开始

### 1. 基本使用

```cpp
#include "utlog.h"

int main() {
    // 设置默认日志器
    UTLOG_SET_LOGPATH("./logs");
    
    // 设置日志输出级别，默认是info级别
    UTLOG_SET_LEVEL(utlog::log_level::debug);

    // 启用控制台输出
    UTLOG_SET_CONSOLE_ENABLE(true);

    // 启用代码位置跟踪
    UTLOG_SET_TRACE_ENABLE(true);

    // 记录不同级别的日志
    UTLOG_INFO("这是一条信息日志");
    UTLOG_DEBUG("这是一条调试日志");
    UTLOG_WARN("这是一条警告日志");
    UTLOG_ERROR("这是一条错误日志");

    return 0;
}
```

### 2. 默认日志路径和文件名

**如果不使用 `UTLOG_SET_LOGPATH` 设置日志路径，系统会使用以下默认规则：**

#### 默认日志目录
- **Windows**: `可执行文件目录\log\`
- **Linux**: `可执行文件目录/log/`

#### 默认日志文件名
- **文件名**: `可执行文件名.log`（不包含扩展名）

#### 示例
假设您的可执行文件为：
- **Windows**: `C:\MyApp\bin\myapp.exe`
  - 默认日志路径: `C:\MyApp\bin\log\myapp.exe.log`
- **Linux**: `/home/<USER>/myapp/bin/myapp`
  - 默认日志路径: `/home/<USER>/myapp/bin/log/myapp.log`

#### 不设置路径的使用示例

```cpp
#include "utlog.h"

int main() {
    // 不设置路径，使用默认路径
    // 日志将保存到: 可执行文件目录/log/程序名.log

    // 启用控制台输出
    UTLOG_SET_CONSOLE_ENABLE(true);

    // 直接使用日志记录
    UTLOG_INFO("使用默认路径的日志记录");

    return 0;
}
```

> **注意**:
> - 如果默认的 `log` 目录不存在，系统会自动创建
> - 确保程序对可执行文件目录有写入权限
> - 可以通过控制台输出查看实际的日志文件路径

### 3. 创建自定义日志器

```cpp
#include "utlog.h"

int main() {
    // 创建自定义日志器
    // 按日期切分：保留 7 天
    auto daily = utlog::createDailyLogger(
        "./logs",           // 日志目录
        "myapp",           // 日志基础文件名
        7,                  // 保留天数
        utlog::log_level::debug
    );
    daily->log("按日期切分-示例", utlog::log_level::info);

    // 仍然支持按大小滚动
    auto rotating = utlog::createLogger(
        "./logs",
        "myapp",
        20 * 1024 * 1024,
        5,
        utlog::log_level::debug
    );
    rotating->log("按大小滚动-示例", utlog::log_level::info);
    
    // 使用自定义日志器
    logger->log("自定义日志器消息", utlog::log_level::info);
    
    return 0;
}
```

## API 参考

### 日志级别

```cpp
namespace utlog::log_level {
    enum level_enum {
        trace = 0,      // 跟踪级别
        debug = 1,      // 调试级别
        info = 2,       // 信息级别
        warn = 3,       // 警告级别
        err = 4,        // 错误级别
        critical = 5    // 严重错误级别
    };
}
```

### 核心函数

#### 日志器管理

```cpp
// 创建日志器
std::shared_ptr<UTLogger> createLogger(
    const std::string& path = "",           // 日志目录路径
    const std::string& filename = "",       // 日志文件名
    int logSize = 20 * 1024 * 1024,        // 单个日志文件大小
    int backupCount = 3,                    // 备份文件数量
    utlog::log_level::level_enum l = utlog::log_level::info  // 默认日志级别
);

// 创建“按日期切分”的日志器（每天生成一个新文件）
std::shared_ptr<UTLogger> createDailyLogger(
    const std::string& path = "",           // 日志目录
    const std::string& filename = "",       // 基础文件名
    int maxDays = 7,                          // 保留天数
    utlog::log_level::level_enum l = utlog::log_level::info
);

// 设置默认日志器
bool setDefultLogger(std::shared_ptr<UTLogger> logger);

// 获取默认日志器
std::shared_ptr<UTLogger> getDefaultLogger();
```

#### 配置函数

```cpp
// 启用/禁用日志记录
bool setLogEnable(bool logFlag);

// 启用/禁用控制台输出
bool setConsoleEnable(bool consoleFlag);

// 启用/禁用代码位置跟踪
bool setTraceEnable(bool traceFlag);

// 设置日志刷新间隔（秒）
bool setFlushInterval(unsigned seconds);
```

#### 控制台输出

```cpp
// 控制台输出（不带位置信息）
bool console(const std::string& data, 
             utlog::log_level::level_enum level = utlog::log_level::debug);

// 控制台输出（带位置信息）
bool console(const std::string& data, 
             UTLog_Trace_Info traceInfo, 
             utlog::log_level::level_enum level = utlog::log_level::debug);
```

### 便捷宏

```cpp
// 设置默认日志器
UTLOG_SET_DEFAULTLOGGER(logger)

// 设置日志路径（自动创建默认日志器）
UTLOG_SET_LOGPATH(path)

// 设置日志级别 默认是info级别
UTLOG_SET_LEVEL(level)

// 各级别日志记录宏（自动包含位置信息）
UTLOG_TRACE(message)     // 跟踪日志
UTLOG_DEBUG(message)     // 调试日志
UTLOG_INFO(message)      // 信息日志
UTLOG_WARN(message)      // 警告日志
UTLOG_ERROR(message)     // 错误日志
UTLOG_CRITICAL(message)  // 严重错误日志

// 控制台输出宏
UTLOG_CONSOLE(message)

// 配置宏
UTLOG_SET_LOG_ENABLE(flag)      // 启用/禁用日志
UTLOG_SET_CONSOLE_ENABLE(flag)  // 启用/禁用控制台
UTLOG_SET_TRACE_ENABLE(flag)    // 启用/禁用位置跟踪
UTLOG_SETFLUSH_INTERVAL(seconds) // 设置刷新间隔
```

## 使用示例

### 示例 1: 基础日志记录

```cpp
#include "utlog.h"

int main() {
    // 初始化日志系统
    UTLOG_SET_LOGPATH("./logs");
    UTLOG_SET_CONSOLE_ENABLE(true);
    UTLOG_SET_TRACE_ENABLE(true);
    UTLOG_SET_LEVEL(utlog::log_level::debug);
    
    // 记录各种级别的日志
    UTLOG_DEBUG("应用程序启动");
    UTLOG_INFO("用户登录成功");
    UTLOG_WARN("内存使用率较高");
    UTLOG_ERROR("数据库连接失败");
    UTLOG_CRITICAL("系统即将崩溃");
    
    return 0;
}
```

### 示例 2: 多日志器使用

```cpp
#include "utlog.h"

int main() {
    // 创建应用日志器
    auto appLogger = utlog::createLogger("./logs", "app", 10*1024*1024, 3);
    
    // 创建错误日志器
    auto errorLogger = utlog::createLogger("./logs", "error", 5*1024*1024, 5, utlog::log_level::err);
    
    // 使用不同的日志器
    appLogger->log("应用程序正常运行", utlog::log_level::info);
    errorLogger->log("发生严重错误", utlog::log_level::err);
    
    return 0;
}
```

### 示例 3: 条件日志记录

```cpp
#include "utlog.h"

void processData(const std::string& data) {
    UTLOG_DEBUG("开始处理数据: " + data);
    
    if (data.empty()) {
        UTLOG_WARN("接收到空数据");
        return;
    }
    
    try {
        // 处理数据的逻辑
        UTLOG_INFO("数据处理成功");
    } catch (const std::exception& e) {
        UTLOG_ERROR("数据处理失败: " + std::string(e.what()));
    }
}
```

## 配置说明

### 日志文件配置

- **日志目录**:
  - 通过 `createLogger` 的 `path` 参数指定
  - 或使用 `UTLOG_SET_LOGPATH` 宏设置
  - **默认路径**: 如果未指定，使用 `可执行文件目录/log/`
- **文件名**:
  - 通过 `filename` 参数指定
  - **默认文件名**: 如果未指定，使用 `可执行文件名.log`
- **按天切分**: 默认每天一个日志文件，文件名包含日期后缀
- **保留天数**: 可通过 `createDailyLogger` 的 `maxDays` 控制历史保留天数
- **按大小滚动**: 通过 `createLogger(logSize, backupCount)` 仍可按大小滚动

#### 路径解析规则

1. **优先级顺序**:
   - 显式指定的路径（`createLogger` 或 `UTLOG_SET_LOGPATH`）
   - 默认路径（可执行文件目录/log/）

2. **路径格式**:
   - Windows: 使用反斜杠 `\` 作为路径分隔符
   - Linux: 使用正斜杠 `/` 作为路径分隔符
   - 系统会自动处理路径分隔符的转换

3. **目录创建**:
   - 如果指定的日志目录不存在，系统会自动递归创建
   - 需要确保程序有相应的文件系统写入权限

### 日志级别配置

日志级别从低到高：trace < debug < info < warn < error < critical

只有等于或高于设置级别的日志才会被记录。

### 性能配置

- **异步模式**: 通过 `setFlushInterval` 设置刷新间隔来启用异步写入
- **位置跟踪**: 可选功能，会影响性能，建议在调试时启用

### 彩色控制台输出（Linux）

UTLog 的彩色输出由 spdlog 的彩色终端 sink 实现（Linux 下为 `ansicolor_sink`，Windows 下为 `wincolor_sink`）。要在控制台看到颜色，需要同时满足：

- 终端为 TTY（`isatty(1)` 为真），且终端类型支持颜色（`TERM`/`COLORTERM` 合适）
- 控制台日志的 pattern 含有颜色标记 `%^` 和 `%$`（用于标记着色范围）

#### 现有实现与影响

- 代码中创建控制台 logger 使用：`spdlog::stdout_color_mt(name, spdlog::color_mode::automatic)`（自动模式）。
- 默认设置的控制台 pattern 为：`"%+"`。

在 spdlog 1.8.x 中，`"%+"` 并不会自动启用颜色范围。即使使用了彩色 sink，只要 pattern 未包含 `%^`/`%$`，就不会输出 ANSI 颜色序列。因此，出现“控制台有日志但无颜色”通常是由 pattern 未设置颜色标记引起的。

#### 推荐做法（不改变对外接口）

如需启用颜色，建议将控制台 logger 的 pattern 改为包含颜色标记，例如：

```cpp
// 示例：在控制台仅为级别着色
m_console->set_pattern("[%Y-%m-%d %H:%M:%S.%e] [%^%l%$] %v");

// 或者为整行着色（不常用）
// m_console->set_pattern("%^%+%$");
```

同时，如有全局 `g_console`，也建议为其设置相同的带颜色标记的 pattern。

> 注：以上为库内部实现处的调整建议，不改变任何对外 API。若您使用的是当前仓库代码，修改位置见 `utils/utlog/src/utlog.cpp` 内控制台 logger 初始化后对 `set_pattern` 的调用。

#### 可选：在非 TTY 环境强制着色

若在 Docker、CI、IDE 集成控制台等非 TTY 环境仍希望输出颜色，可将创建控制台 logger 时的颜色模式由 `automatic` 改为 `always`：

```cpp
// 将 automatic 改为 always，可在非 TTY 环境强制输出 ANSI 颜色
m_console = spdlog::stdout_color_mt(name + "_console", spdlog::color_mode::always);
```

> 风险提示：在非 TTY 环境强制着色可能导致日志中出现原始 ANSI 转义序列，影响可读性或后续解析。

#### Linux 环境检查清单

- 运行环境是否为 TTY：
  - `test -t 1 && echo tty || echo notty`（若为 `notty`，自动着色会被关闭）
- 终端变量是否合理：
  - 建议设置 `TERM` 为支持颜色的值：`export TERM=xterm-256color`
  - 可选：`export COLORTERM=truecolor`
- 避免使用重定向/管道：
  - 如 `./app | tee out.log`、`./app > out.log` 会使 `isatty(1)` 变为假，自动着色被关闭
- 在 IDE（如 Qt Creator）内运行：
  - IDE 的“应用输出”面板通常不是 TTY，建议在外部终端运行以验证颜色

#### 跨平台差异

- Windows：使用 `wincolor_sink`，也依赖是否有控制台句柄和 `isatty`。pattern 规则与 Linux 相同，同样需要 `%^`/`%$` 才会着色。
- Linux：使用 `ansicolor_sink`，除 `isatty` 外，还通过 `TERM`/`COLORTERM` 判断是否支持颜色。

#### 最小可用配置步骤（Linux）

1. 在外部终端运行程序，确保是 TTY 环境：`test -t 1 && echo tty`
2. 确保环境变量：`export TERM=xterm-256color`（必要时再设置 `COLORTERM=truecolor`）
3. 代码中启用控制台输出：`UTLOG_SET_CONSOLE_ENABLE(true)`
4. 将控制台 pattern 设置为包含颜色标记（库内部实现处）：

```cpp
// utils/utlog/src/utlog.cpp 内控制台 logger 初始化后：
m_console->set_pattern("[%Y-%m-%d %H:%M:%S.%e] [%^%l%$] %v");
```

5. 若在非 TTY 环境也需要颜色，可将 `color_mode::automatic` 改为 `color_mode::always`（可选）。

## 注意事项

1. **线程安全**: 该库是线程安全的，可以在多线程环境中使用
2. **内存管理**: 使用智能指针管理日志器生命周期
3. **文件权限**: 确保程序有权限在指定目录创建和写入文件
4. **磁盘空间**: 注意监控日志文件占用的磁盘空间
5. **性能考虑**: 在高频日志场景下，建议适当调整日志级别和刷新间隔

## 故障排除

### 常见问题

1. **编译错误**: 检查是否正确设置了 UTSTENV 环境变量
2. **链接错误**: 确保 ututil 库已正确编译并可链接
3. **日志文件未生成**:
   - 检查目录权限和磁盘空间
   - 确认默认日志目录 `可执行文件目录/log/` 是否可写
   - 查看控制台输出的日志路径信息
4. **控制台无输出**: 确保调用了 `UTLOG_SET_CONSOLE_ENABLE(true)`
5. **找不到日志文件**:
   - 检查是否在正确的默认路径查找
   - 使用绝对路径设置日志目录
   - 查看程序启动时的控制台输出，会显示实际的日志文件路径

### 调试建议

1. 使用 `UTLOG_SET_TRACE_ENABLE(true)` 启用位置跟踪
2. 设置较低的日志级别（如 debug）来获取更多信息
3. 检查日志文件的实际输出内容

## 许可证

本项目遵循相应的开源许可证，具体请查看项目根目录的 LICENSE 文件。

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

---

*最后更新: 2024年*
