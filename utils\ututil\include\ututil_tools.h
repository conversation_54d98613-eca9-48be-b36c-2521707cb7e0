﻿#ifndef __UTUTIL_TOOLS_H__
#define __UTUTIL_TOOLS_H__

// #define USER_MFCSTRING // 使用MFC CString（废弃），已用MFC中宏AFXAPI替代
// #define EXPORT_UTUTIL	  // Windows导出dll时请定义此宏 (目前在项目配置文件中定义)

#if defined(__aarch64__)// Linux(ARM64)
#	ifndef LINUX
#		define LINUX
#	endif
#endif

#if defined (_WIN32)
#	ifndef WIN32
#		define WIN32
#	endif
#elif defined (__linux) || defined(__aarch64__)// Linux(ARM64)
#	ifndef LINUX
#		define LINUX
#	endif
#else
//unsupport os
#endif

#ifndef WIN32
#	ifndef LINUX
#		define LINUX
#	endif
#endif

// Virtual c++
#if defined(_MSC_VER)
#	if (_MSC_VER >= 1700)
#		ifndef STD11
#			define STD11
#		endif
#  endif
#	ifndef WIN32
#		define WIN32
#	endif
#endif

// g++

#if (__GNUC__ >= 5)
#	ifndef STD11
#		define STD11
#	endif
#define  __STDC_WANT_LIB_EXT1__ 1
#endif

#ifdef __x86_64__
#	if !defined(_W64)
#		define _W64
#	endif
#endif

#define USE_UTUTIL	using namespace ututil

#define DEF_BEG_UTUTIL namespace ututil {

#define DEF_END_UTUTIL }

#ifdef STD11
#	define	STD_MOVE(object) std::move(object)
#else
#	define	STD_MOVE(object) object
#endif

/*************************************************
*备注
*WCHAR暂时只支持WINDOWS
*************************************************/


#ifdef WIN32
#	include <string.h>
#	include <tchar.h>
#	include <io.h>
#	include <WinSock2.h>
#	include <WS2tcpip.h>
#	include <windef.h>
#	include <time.h>
#	include <stdarg.h>
#	include <stdio.h>
#	include <stdarg.h>
#if _MSC_VER > 1500
#	include <cstdint>
#endif
#elif defined(LINUX)
#	include <string.h>
#	include <sys/types.h>
#	include <dirent.h>
#	include <fcntl.h>
#	include <unistd.h>
#	include <sys/stat.h>
#	include <wchar.h>
#	include <time.h>
#ifdef STD11
#	include <cstdint>
#endif
#	include <stdio.h>
#	include <sys/socket.h>
#	include <errno.h>
#	include <netinet/in.h>
#	include <stdarg.h>
#endif

#if defined(WIN32) && !defined(STATIC_UTUTIL)
#	ifdef UTUTIL_EXPORT
#		define UTUTIL __declspec(dllexport)
#	else
#		define UTUTIL __declspec(dllimport)
#	endif
#elif defined(LINUX) || defined(STATIC_UTUTIL)
#	define UTUTIL
#endif
#include "ututil_warning.h"


#define EXTERNCPP		extern "C++" {
#define END_EXTERNCPP   }

#ifndef __USE_LARGEFILE64
#	define __USE_LARGEFILE64 // 支持LINUX open64类系列函数
#endif


#define USERERRORNO	130

#define PARAMUNINIT	0xFFFFFFFF ///< 参数未初始化

/** 
 * @name 空指针定义
 * @{
 */
#ifdef STD11
#	define NullPtr nullptr
#else
#	define NullPtr NULL
#endif
/** @} */

#define UTUTIL_HANDLE void*
/**
 * @name windows已有类型定义、宏定义
 * @{
 */
#ifndef WIN32
	typedef int LONG;
	typedef long long LONGLONG;
	typedef unsigned long long UINT64;
	typedef wchar_t WCHAR;
	typedef long long INT64;
	typedef unsigned char BYTE;
	typedef unsigned int DWORD;
	typedef unsigned long long DWORD64;
	typedef unsigned long ULONG;
	typedef unsigned int BOOL;
	typedef unsigned short USHORT;
	typedef unsigned int UINT;
	typedef unsigned int UINT32;
	typedef long intptr;
	typedef sockaddr_in SOCKADDR_IN;
	typedef sockaddr_in6 SOCKADDR_IN6;
	typedef sockaddr	SOCKADDR;
	typedef sockaddr_storage SOCKADDR_STORAGE;
	#define CHAR char
	#ifndef TRUE
		#define TRUE 1
	#endif
	#ifndef FALSE
		#define FALSE 0
	#endif
	#define MAX_PATH 256
	#define __stdcall
	#define __clrcall
	#define _stdcall
	#define _clrcall
	#define _Out_
	#define _In_
	#define _snprintf snprintf
	/*********同步函数操作宏定义 begin********/
	#define INFINITE 0xFFFFFFFF			// 事件一直等待直到有信号为止
	#define WAIT_OBJECT_0 0				// 等待成功
	#define WAIT_TIMEOUT 0x00000102L	// 等待超时
	#define WAIT_FAILED  0xFFFFFFFF		// 等待失败，发生错误
	#define MAXIMUM_WAIT_OBJECTS 64
	/*********同步函数操作宏定义 end********/

	//路径
	#define _getcwd	getcwd
	#define _wgetcwd wgetcwd
	#define _access access
	// 字符串、文件操作
	#if defined(UNICODE) || defined(_UNICODE)
		#define TCHAR WCHAR
		#define _tcslen wcslen
		#define _tcscpy wcscpy
		#define _tcscat wcscat
		#define _taccess waccess
		#define _tprintf wprintf
		#define _tcscmp wcscmp
		#define _tcsncmp wcsncmp
		#define _tcsncpy wcsncpy
		#define _sntprintf swprintf
		#define tcscpy_safe wcscpy_safe
		#define tsprintf_safe wsprintf_safe
		#define TEXT(str) L##str
		#define _tfopen	wfopen
	#else
		#define  TCHAR CHAR
		#define _tcscmp strcmp
		#define _tcslen strlen
		#define _tcscpy strcpy
		#define _tcscat strcat
		#define _taccess access
		#define _tcsncmp strncmp
		#define _tprintf printf
		#define TEXT(str) str
		#define _sntprintf snprintf
		#define tsprintf_safe sprintf_safe
		#define _tcsncpy strncpy
		#define tcscpy_safe strcpy_safe
		#define _tfopen fopen
	#endif // endif UNICODE || _UNICODE
	typedef union _LARGE_INTEGER {
		struct {
			DWORD LowPart;
			LONG HighPart;
		} DUMMYSTRUCTNAME;
		struct {
			DWORD LowPart;
			LONG HighPart;
		} u;
		LONGLONG QuadPart;
	} LARGE_INTEGER;

#	ifndef INVALID_SOCKET
#		define INVALID_SOCKET (SOCKET)(~0)
#	endif
#	ifndef SOCKET
#		define SOCKET int
#	endif
#else
#	if defined(UNICODE) || defined(_UNICODE)
#		define tcscpy_safe wcscpy_safe
#		define tsprintf_safe wsprintf_safe
#	else
#		define tcscpy_safe strcpy_safe
#		define tsprintf_safe sprintf_safe
#	endif
#endif // ifndef WIN32

#ifdef WIN32
#define snprintf _snprintf
#endif

// 指针定义
#ifdef LINUX
#	if defined(_W64)
#		define INTPTR_T INT64
#		define UINTPTR_T UINT64
#	else
#		define INTPTR_T int
#		define UINTPTR_T UINT
#	endif
typedef INTPTR_T fsize_t;
#endif // endif linux intptr_t

#ifdef WIN32
#	if defined(_WIN64)
#		define INTPTR_T INT64
#		define UINTPTR_T UINT64
#	else
#		define INTPTR_T int
#		define UINTPTR_T UINT
#	endif
typedef INTPTR_T fsize_t;
#endif // endif WIN32 intptr_t

#ifdef LINUX
#	define long_t long
#elif defined WIN32
#	if defined(_WIN64)
#		define long_t INT64
#	else
#		define long_t int
#	endif
#endif // endif long_t
/** @} */

/**
 * @name 浮点精度宏定义
 * @{
 */
#define FLT_DOT	0.0000001						// 7位
#define MINUX_FLT_DOT -0.00000001				// 8位
#define DOE_DOT 0.000000000000001				// 15位
#define MINUX_DOE_DOT -0.0000000000000001		// 16位
#define DOE_DOT_COUNT	15
#define FLT_DOT_COUNT	7
#define NORMAL_DOT_COUNT 6						// 常用6位
/** @} */

/**
 * @name 文件打开模式
 * @{
 */
#define FILE_ONLYREAD 1				///< 只读
#define FILE_ONLYWRITE 2			///< 只写
#define FILE_READWRITE  4			///< 读写
#define FILE_SHARE	 8				///< 共享,WINDOWS上有效，linux忽略
#define FILE_INDEPENDENT 16 		///< 排它性，只在windows有效，linux忽略
#define FILE_TRUC 32				///< 清空
#define FILE_HIDE 64				///< 隐藏文件，windows中有效
#define FILE_ASYN	128				///< 异步方式打开文件
#define FILE_CREATEPLUS 256			///< 如果文件不存在则创建此文件，存在则打开它
#define FILE_EXCL		512			///< 与FILE_CREATEPLUS一起使用，如果存在文件则失败，不存在则创建

#define FILE_SEEK_CUR	1			///< 定位当前位置
#define FILE_SEEK_SET	0			///< 定位开始位置
#define FILE_SEEK_END	2			///< 定位到结束位置
/** @} */

/**
 * @name 文件属性
 * @{
 */
#define FILE_DIR	 0				///< 目录
#define FILE_NORMAL	 1				///< 常规的存档文件
#define FILE_SYSTYPE 2				///< 系统其它文件
/** @} */


//! 文件句柄
#define FILE_HANDLE intptr_t

/**
 * @brief 文件查找信息
 */
typedef struct _FILEFINDINFO_
{
	TCHAR szFileName[MAX_PATH];		///< 文件名称
	DWORD fwFileAttributes;			///< 文件属性 参见:文件属性
	INT64 dwFilesize;				///< 文件大小,如果是目录统一为0字节
	time_t tCreateTime;				///< 文件创建的时间
	time_t tUpdateTime;				///< 文件修改的时间
}FILEFINDINFO, *PFILEFINDINFO;

/**
 * @name 线程句柄和其它同步对象句柄、函数指针
 * @{
 */ 
#ifdef WIN32
#	define THREADPTR UINTPTR_T
#elif defined(LINUX)
#	define THREADPTR pthread_t
#endif // endif THREADPTR
#define SYNCHANDLE INTPTR_T  ///< 异步函数句柄

#define THREAD_STACK_MIN 8192*1024	///< 线程最小的堆栈

//! 线程函数指针
typedef DWORD(*ThreadRoutine)(void* arg);
/** @} */
/*********************************************************/

/** 
 * @name 屏蔽某个类的拷贝赋值和拷贝构造
 * @{
 */
#ifdef STD11
#	define delete_class_copy_operator(ClassName) ClassName(const ClassName &)=delete; ClassName & operator= (const ClassName &) = delete
#else
#	define delete_class_copy_operator(ClassName)  private: ClassName(const ClassName &); ClassName & operator = (const ClassName &)
#endif
/** @} */

/**
 * @name 字符串安全拷贝操作
 * @{
 * @param dst 目标串
 * @param nBufSize 目标串的缓冲区大小
 * @param src 源串
 * @param srcLen 源串需要拷贝的大小,若srcLen为0则使用strlen进行计算
 */
inline char* strcpy_safe(char *dst, size_t nBufSize, const char* src, size_t srcLen = 0)
{
#if defined(WIN32) || (defined(STD11) && defined(__STDC_LIB_EXT1__))
	strcpy_s(dst, nBufSize, src);
#else
	if (srcLen == 0) srcLen = strlen(src);
	strncpy(dst, src, nBufSize < srcLen ? nBufSize : srcLen);
	*(dst + nBufSize - 1) = '\0';
#endif
	return dst;
}

inline WCHAR* wcscpy_safe(WCHAR *dst, size_t nBufSize, const WCHAR* src, size_t srcLen = 0)
{
#if defined(WIN32) || (defined(STD11) && defined(__STDC_LIB_EXT1__))
	wcscpy_s(dst, nBufSize, src);
#else
	if (srcLen == 0) srcLen = wcslen(src);
	wcsncpy(dst, src, nBufSize < srcLen ? nBufSize : srcLen);
	*(dst + nBufSize - 1) = '\0';
#endif
	return dst;
}

inline size_t sprintf_safe(char *str, size_t size, const char *format, ...)
{
	va_list params;
	va_start(params, format);
#if defined(WIN32) || (defined(STD11) && defined(__STDC_LIB_EXT1__))
	size = vsprintf_s(str, size, format, params);
#elif defined(LINUX)
	int n = vsnprintf(str, size, format, params);
	if (str[size - 1] != L'\0') {
		str[size - 1] = L'\0';
	}
	else {
		size = n;
	}
#endif
	va_end(params);
	return size;
}

inline size_t sprintfv_safe(char *str, size_t size, const char *format, va_list& params)
{
#if defined(WIN32) || (defined(STD11) && defined(__STDC_LIB_EXT1__))
	size = vsprintf_s(str, size, format, params);
#elif defined(LINUX)
	int n = vsnprintf(str, size, format, params);
	if (str[size - 1] != L'\0') {
		str[size - 1] = L'\0';
	}
	else {
		size = n;
	}
#endif
	return size;
}

inline size_t wsprintfv_safe(WCHAR *str, size_t size, const WCHAR *format, va_list& params)
{
#if defined(WIN32) || (defined(STD11) && defined(__STDC_LIB_EXT1__))
	size = vswprintf_s(str, size, format, params);
#elif defined(LINUX)
	int n = vswprintf(str, size, format, params);
	if (str[size - 1] != L'\0') {
		str[size - 1] = L'\0';
	}
	else {
		size = n;
	}
#endif
	return size;
}

inline size_t wsprintf_safe(WCHAR *str, size_t size, const WCHAR *format, ...)
{
	va_list params;
	va_start(params, format);
#if defined(WIN32) || (defined(STD11) && defined(__STDC_LIB_EXT1__))
	size = vswprintf_s(str, size, format, params);
#elif defined(LINUX)
	int n = vswprintf(str, size, format, params);
	if (str[size - 1] != L'\0') {
		str[size - 1] = L'\0';
	}
	else {
		size = n;
	}
#endif
	va_end(params);
	return size;
}
/** @} */


#endif
