﻿#include "ututil_json.h"

USE_UTUTIL;

std::string CJsonUtil::jsonToString(JsonCpp::Value &json)
{
    JsonCpp::StreamWriterBuilder builder;
    std::unique_ptr<JsonCpp::StreamWriter> writer(builder.newStreamWriter());
    std::ostringstream os;
    writer->write(json, &os);
    return os.str();
}

bool CJsonUtil::stringToJson(const char * str, JsonCpp::Value * json)
{
    JsonCpp::CharReaderBuilder builder;
    std::unique_ptr<JsonCpp::CharReader> const reader(builder.newCharReader());

    std::string error;
    if(!reader->parse(str, str+strlen(str), json, &error))
        return false;
    return true;
}
