# DataManager 数据管理系统使用指南

## 概述

DataManager是智能运载小车控制端界面系统的核心数据管理模块，采用单例模式设计，提供统一的数据访问接口、SQLite数据持久化、数据变更通知机制和线程安全的数据访问。

## 架构设计

### 核心组件

1. **DataManager** - 主要数据管理器，单例模式
2. **数据模型类** - 五个核心数据模型类：
   - VideoData - 视频数据
   - StatusData - 系统状态数据
   - DistanceData - 距离传感器数据
   - ControlData - 控制命令数据
   - DebugSettingsData - 调试设置数据
3. **SQLite数据库** - 数据持久化存储
4. **信号槽机制** - 数据变更通知

### 特性

- ✅ **单例模式** - 全局统一的数据访问点
- ✅ **线程安全** - 使用QMutex保护数据访问
- ✅ **自动持久化** - 支持自动保存和手动保存
- ✅ **信号通知** - Qt信号槽机制实现数据变更通知
- ✅ **数据库支持** - SQLite数据库持久化存储
- ✅ **环境变量集成** - 使用$UTSTENV环境变量管理路径

## 快速开始

### 1. 基本使用

```cpp
#include "datamanager.h"
#include "datamodels.h"

// 获取DataManager实例
DataManager* dataManager = DataManager::instance();

// 初始化DataManager
if (!dataManager->initialize()) {
    qDebug() << "Failed to initialize DataManager";
    return;
}

// 启用自动保存（每30秒保存一次）
dataManager->setAutoSave(true, 30000);

// 获取数据模型
StatusData* statusData = dataManager->statusData();
VideoData* videoData = dataManager->videoData();
DistanceData* distanceData = dataManager->distanceData();
ControlData* controlData = dataManager->controlData();
DebugSettingsData* debugData = dataManager->debugSettingsData();
```

### 2. 数据更新

```cpp
// 更新状态数据
statusData->setSystemStatus(StatusData::Online);
statusData->setConnected(true);
statusData->setDeviceStatus("设备正常运行");

// 更新距离数据
distanceData->setFrontDistance(15.5);
distanceData->setRearDistance(12.3);
distanceData->setSensorOnline(true);

// 更新控制数据
controlData->setMotionMode(ControlData::Automatic);
controlData->setSpeed(8.5);
controlData->setCurrentCommand("自动巡航");
```

### 3. 数据监听

```cpp
// 连接数据变更信号
connect(dataManager, SIGNAL(statusDataChanged()), 
        this, SLOT(onStatusDataChanged()));
connect(dataManager, SIGNAL(distanceDataChanged()), 
        this, SLOT(onDistanceDataChanged()));

// 实现响应槽函数
void MyClass::onStatusDataChanged()
{
    StatusData* statusData = dataManager->statusData();
    bool isConnected = statusData->isConnected();
    QString deviceStatus = statusData->deviceStatus();
    
    // 更新界面显示
    updateUIStatus(deviceStatus, isConnected);
}
```

## 在插件中使用DataManager

### 插件集成示例（以HeaderAreaPlugin为例）

#### 1. 修改插件头文件

```cpp
// headerareaplugin.h
class DataManager;

class HeaderAreaPlugin : public QObject, public IPlugin
{
    Q_OBJECT
    Q_INTERFACES(IPlugin)

public:
    // ... 其他方法

private slots:
    void onStatusDataChanged();

private:
    DataManager *m_dataManager;
    // ... 其他成员
};
```

#### 2. 修改插件实现

```cpp
// headerareaplugin.cpp
#include "datamanager.h"

bool HeaderAreaPlugin::initialize()
{
    // 获取DataManager实例
    m_dataManager = DataManager::instance();
    if (!m_dataManager->initialize()) {
        UTLOG_ERROR("Failed to initialize DataManager");
        return false;
    }

    // 启用自动保存
    m_dataManager->setAutoSave(true, 30000);

    // 连接数据变更信号
    connect(m_dataManager, SIGNAL(statusDataChanged()), 
            this, SLOT(onStatusDataChanged()));

    return true;
}

QWidget* HeaderAreaPlugin::createWidget(QWidget* parent)
{
    m_widget = new HeaderAreaWidget(parent);
    
    // 将DataManager传递给Widget
    if (m_dataManager) {
        m_widget->setDataManager(m_dataManager);
    }
    
    return m_widget;
}

void HeaderAreaPlugin::onStatusDataChanged()
{
    // 处理状态数据变更
    StatusData* statusData = m_dataManager->statusData();
    if (statusData && m_widget) {
        m_widget->setSystemStatus(
            statusData->deviceStatus(), 
            statusData->isConnected()
        );
    }
}
```

#### 3. 项目配置

确保插件项目文件(.pro)包含对core库的依赖：

```pro
# headerareaplugin.pro
LIBS += -L$$(UTSTENV)/shlib \
        -lcore \
        -lutlog

INCLUDEPATH += $$(UTSTENV)/interfaces \
               $$(UTSTENV)/core \
               $$(UTSTENV)/utils/utlog/include
```

## 数据模型详解

### VideoData - 视频数据模型

```cpp
// 主要属性
QString videoSource()          // 视频源
bool isRecording()            // 录制状态
QString resolution()          // 分辨率
int frameRate()              // 帧率
QPixmap currentFrame()       // 当前帧

// 主要信号
void frameUpdated(const QPixmap &frame);
void recordingStateChanged(bool recording);
void videoSourceChanged(const QString &source);
```

### StatusData - 状态数据模型

```cpp
// 系统状态枚举
enum SystemStatus {
    Unknown = 0,      // 未知
    Offline = 1,      // 离线
    Online = 2,       // 在线
    Error = 3,        // 错误
    Maintenance = 4   // 维护中
};

// 主要属性
SystemStatus systemStatus()   // 系统状态
bool isConnected()           // 连接状态
QString deviceStatus()       // 设备状态
QString errorMessage()       // 错误信息
int upTime()                // 运行时间

// 主要信号
void systemStatusChanged(int status);
void connectionStateChanged(bool connected);
void deviceStatusChanged(const QString &status);
void errorOccurred(const QString &message);
```

### DistanceData - 距离数据模型

```cpp
// 主要属性
double frontDistance()       // 前方距离
double rearDistance()        // 后方距离
double leftDistance()        // 左侧距离
double rightDistance()       // 右侧距离
double height()             // 高度
bool isSensorOnline()       // 传感器在线状态

// 主要信号
void distanceChanged(double front, double rear, double left, double right);
void heightChanged(double height);
void sensorStatusChanged(bool online);
```

### ControlData - 控制数据模型

```cpp
// 运动模式枚举
enum MotionMode {
    Manual = 0,      // 手动模式
    Automatic = 1,   // 自动模式
    Remote = 2       // 远程模式
};

// 主要属性
MotionMode motionMode()      // 运动模式
double speed()              // 速度
double direction()          // 方向
QString currentPosition()   // 当前位置
QString targetPosition()    // 目标位置
QString currentCommand()    // 当前命令
bool isExecuting()         // 执行状态

// 主要信号
void motionModeChanged(int mode);
void speedChanged(double speed);
void directionChanged(double direction);
void positionChanged(const QString &position);
void commandChanged(const QString &command);
void executionStateChanged(bool executing);
```

### DebugSettingsData - 调试设置数据模型

```cpp
// 日志级别枚举
enum LogLevel {
    Trace = 0,
    Debug = 1,
    Info = 2,
    Warning = 3,
    Error = 4,
    Critical = 5
};

// 主要属性
LogLevel logLevel()                    // 日志级别
bool isDebugMode()                    // 调试模式
bool isPerformanceMonitoring()       // 性能监控
int communicationTimeout()           // 通信超时
int retryCount()                     // 重试次数
QVariantMap customSettings()         // 自定义设置

// 自定义设置操作
void setCustomSetting(const QString &key, const QVariant &value);
QVariant getCustomSetting(const QString &key, const QVariant &defaultValue);

// 主要信号
void logLevelChanged(int level);
void debugModeChanged(bool debug);
void performanceMonitoringChanged(bool enabled);
void communicationTimeoutChanged(int timeout);
void retryCountChanged(int count);
void customSettingChanged(const QString &key, const QVariant &value);
```

## 数据库结构

DataManager使用SQLite数据库进行数据持久化，数据库文件默认位置：
- 优先：`$UTSTENV/data/utsmarttrolley.db`
- 备用：`应用程序目录/data/utsmarttrolley.db`

### 数据库表结构

#### video_data 表
```sql
CREATE TABLE video_data (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    video_source TEXT,
    is_recording INTEGER DEFAULT 0,
    resolution TEXT,
    frame_rate INTEGER DEFAULT 30,
    last_updated TEXT
);
```

#### status_data 表
```sql
CREATE TABLE status_data (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    system_status INTEGER DEFAULT 0,
    is_connected INTEGER DEFAULT 0,
    device_status TEXT,
    error_message TEXT,
    up_time INTEGER DEFAULT 0,
    last_updated TEXT
);
```

#### distance_data 表
```sql
CREATE TABLE distance_data (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    front_distance REAL DEFAULT 0.0,
    rear_distance REAL DEFAULT 0.0,
    left_distance REAL DEFAULT 0.0,
    right_distance REAL DEFAULT 0.0,
    height REAL DEFAULT 0.0,
    sensor_online INTEGER DEFAULT 0,
    last_updated TEXT
);
```

#### control_data 表
```sql
CREATE TABLE control_data (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    motion_mode INTEGER DEFAULT 0,
    speed REAL DEFAULT 0.0,
    direction REAL DEFAULT 0.0,
    current_position TEXT,
    target_position TEXT,
    current_command TEXT,
    is_executing INTEGER DEFAULT 0,
    last_updated TEXT
);
```

#### debug_settings_data 表
```sql
CREATE TABLE debug_settings_data (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    log_level INTEGER DEFAULT 2,
    debug_mode INTEGER DEFAULT 0,
    performance_monitoring INTEGER DEFAULT 0,
    communication_timeout INTEGER DEFAULT 5000,
    retry_count INTEGER DEFAULT 3,
    custom_settings TEXT,
    last_updated TEXT
);
```

## 构建和编译

### 1. 环境配置

```bash
# 设置UTSTENV环境变量
export UTSTENV=/path/to/your/project/root

# 创建输出目录
mkdir -p $UTSTENV/exec
mkdir -p $UTSTENV/shlib
mkdir -p $UTSTENV/data
```

### 2. 编译core库

```bash
cd $UTSTENV/core
qmake core.pro
make
```

### 3. 编译插件

```bash
cd $UTSTENV/plugins/headerareaplugin
qmake headerareaplugin.pro
make
```

### 4. 运行演示程序

```bash
cd $UTSTENV/main
qmake datamanager_demo.pro
make
$UTSTENV/exec/datamanager_demo
```

## 演示程序

项目包含了一个完整的DataManager演示程序 (`datamanager_demo.cpp`)，展示了：

1. **初始化DataManager**
2. **数据模型的创建和更新**
3. **自动保存和手动保存**
4. **信号槽通信机制**
5. **数据持久化验证**

运行演示程序可以看到：
- 各种数据的模拟更新
- 数据变更信号的触发
- 自动保存和手动保存的执行
- 数据库的创建和数据存储

## 最佳实践

### 1. 初始化顺序
1. 先初始化DataManager
2. 启用自动保存
3. 连接数据变更信号
4. 开始使用数据模型

### 2. 线程安全
- 所有数据模型都是线程安全的
- 可以在不同线程中安全访问
- 使用信号槽进行线程间通信

### 3. 性能优化
- 批量更新数据时可以临时禁用自动保存
- 合理设置自动保存间隔
- 避免频繁的数据库操作

### 4. 错误处理
- 检查DataManager初始化结果
- 监听数据库错误信号
- 处理空指针情况

### 5. 资源管理
- 应用退出前调用cleanup()
- 不需要手动删除数据模型
- DataManager会自动管理资源

## 故障排除

### 常见问题

1. **DataManager初始化失败**
   - 检查UTSTENV环境变量是否设置
   - 确认数据库目录有写权限
   - 查看日志文件中的错误信息

2. **数据库连接失败**
   - 检查SQLite库是否正确链接
   - 确认数据库文件路径
   - 验证目录创建权限

3. **插件加载DataManager失败**
   - 确认core库正确编译和链接
   - 检查插件.pro文件的LIBS配置
   - 验证INCLUDEPATH是否正确

4. **数据变更信号不触发**
   - 确认信号槽连接正确
   - 检查数据是否真的发生了变化
   - 验证线程上下文

### 调试技巧

1. **启用详细日志**
```cpp
UTLOG_SET_LEVEL(utlog::log_level::trace);
UTLOG_SET_CONSOLE_ENABLE(true);
```

2. **检查数据库内容**
```bash
sqlite3 $UTSTENV/data/utsmarttrolley.db
.tables
SELECT * FROM status_data;
```

3. **监控数据变更**
```cpp
connect(dataManager, SIGNAL(dataChanged()), 
        this, SLOT(onAnyDataChanged()));
```

## 扩展DataManager

### 添加新的数据模型

1. 在 `datamodels.h` 中定义新的数据模型类
2. 在 `datamodels.cpp` 中实现数据模型
3. 在 `datamanager.h` 中添加访问接口
4. 在 `datamanager.cpp` 中实现数据管理逻辑
5. 更新数据库表结构

### 添加新的持久化策略

可以扩展DataManager支持其他存储方式：
- XML文件
- JSON文件
- 网络存储
- 其他数据库

## 总结

DataManager为智能运载小车控制端界面系统提供了一个强大、可靠、易用的数据管理解决方案。通过统一的接口、自动持久化和实时通知机制，大大简化了数据管理的复杂性，提高了系统的可维护性和可扩展性。
