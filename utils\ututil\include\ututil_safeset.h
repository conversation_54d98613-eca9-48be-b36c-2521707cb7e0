﻿#ifndef __UTUTIL_SAFESET_H__
#define __UTUTIL_SAFESET_H__
#include "ututil_mutex.h"
#include <set>

DEF_BEG_UTUTIL

/**
 * @brief 线程安全的集合类
 */
template<class Kt, class Ft = std::less<Kt> >
class CSafeSet
{
public:
	//! 插入一个元素
	void Insert(const Kt& key)
	{
		CLockGurad lock(&_mutexSet);
		_setData.insert(key);
	}

	/**
	 * @brief 清除指定键元素
	 * @param key 查找的键
	 * @return 如果存在该元素则返回true,不存在则返回false
	*/
	bool Erase(const Kt& key)
	{
		CLockGurad lock(&_mutexSet);
		typename std::set<Kt, Ft>::iterator it = _setData.find(key);
		if (it != _setData.end())
		{
			_setData.erase(key);
			return true;
		}

		return false;
	}

	/**
	 * @brief 删除第一个元素元素
	 * @param value 返回的结果值
	 * @return 成功返回true,失败返回false
	*/
	bool Pop(Kt& value)
	{
		CLockGurad lock(&_mutexSet);
		if (_setData.size() > 0)
		{
			value = *_setData.begin();
			_setData.erase(_setData.begin());
			return true;
		}
		return false;
	}

	//! 判断键是否存在集合中
	bool Find(const Kt& key)
	{
		CLockGurad lock(&_mutexSet);
		if (_setData.find(key) == _setData.end()) return false;

		return true;
	}

	//! 清空所有元素
	void Clear()
	{
		CLockGurad lock(&_mutexSet);
		_setData.clear();
	}

	//! 返回当前大小
	std::size_t Size()
	{
		CLockGurad lock(&_mutexSet);
		return _setData.size();
	}
public:
	CMutex _mutexSet;
	std::set<Kt, Ft> _setData;
};

DEF_END_UTUTIL

#endif // __SAFESET_H__
