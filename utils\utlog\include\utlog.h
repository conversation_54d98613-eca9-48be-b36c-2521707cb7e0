#ifndef LOGHELPER_H
#define LOGHELPER_H
#include <string>
#include <memory>
/*
  <AUTHOR> 20210326
*/
#ifdef _WIN32
#if defined(UTLOG_LIBRARY)
#  define UTLOG_EXPORT __declspec(dllexport)
#else
#  define UTLOG_EXPORT __declspec(dllimport)
#endif
#else//win32
#define UTLOG_EXPORT
#endif //WIN32
/*
  <AUTHOR>
 * 20010408
 * 默认的告警级别的info，cpp位置跟踪及debug需要自己设置开启，接口已经开放
 * 如果需要多个日志，则使用createLogger
 * 注意：
 * 1、console默认不启动，需要自己setconsoleenable，默认不跟踪
 * 2、跟踪需要自己启动
 * 3、多个日志读写需要自己创建logger，宏不支持
 * 4、支持异步操作
*/
namespace utlog
{
        namespace  log_level
        {
                /*
                 <AUTHOR>
                * 日期：20210512
                * 说明：日志级别枚举
                */
                enum level_enum
                {
                        trace = 0,
                        debug = 1,
                        info = 2,
                        warn = 3,
                        err = 4,
                        critical= 5
                };
        }

        /*
         <AUTHOR>
        * 日期：20210512
        * 类说明：追踪信息结构
        */
        struct UTLOG_EXPORT UTLog_Trace_Info
        {
                UTLog_Trace_Info() = default;
                UTLog_Trace_Info(const char *filename_in, int line_in, const char *funcname_in)
                        :filename(filename_in)
                        , line(line_in)
                        , funcname(funcname_in)
                {}
                const char *filename;
                int line;
                const char *funcname;
        };

        /*
         <AUTHOR>
        * 日期：20210512
        * 类说明：日志操作类抽象
        */
        class UTLOG_EXPORT UTLogger
        {
        public:

                /**
                  <AUTHOR> 2021/05/06
                  @brief 写日志
                  @param data，日志内容
                  @param l， 日志级别，默认为info
                */
                virtual bool log(const std::string& data, utlog::log_level::level_enum = utlog::log_level::info) = 0;

                /**
                  <AUTHOR> 2021/05/06
                  @brief 写日志
                  @param data，日志内容
                  @param traceInfo
                  @param l， 日志级别，默认为info
                */
                virtual bool log(const std::string& data, UTLog_Trace_Info traceInfo, utlog::log_level::level_enum l = utlog::log_level::info) = 0;

                /**
                  <AUTHOR> 2021/05/06
                  @brief 设置默认日志级别
                  @param 日志级别
                */
                virtual bool setLevel(utlog::log_level::level_enum l) = 0;


                                template<class T>
                                bool operator <<(T&& data)
                                {
                                        this->log(std::move(data), utlog::log_level::debug);
                                        return true;
                }
        };

        /**
         <AUTHOR> 2021/05/06
         @brief 设置默认日志操作对象，全局
         @param 日志操作对象实例
        */
        UTLOG_EXPORT bool setDefultLogger(std::shared_ptr<UTLogger>);

        /**
         <AUTHOR> 2021/05/06
         @brief打开追踪，全部级别都可追踪
         @param开启标志
        */
        UTLOG_EXPORT bool setTraceEnable(bool traceFlag);//开关cpp位置跟踪

        /**
         <AUTHOR> 2021/05/06
         @brief打开追踪，全部级别都可追踪
         @param开启标志
        */
        UTLOG_EXPORT  bool setConsoleEnable(bool consoleFlag);//开关打印

        /**
         <AUTHOR> 2021/05/06
         @brief 创建logger
         @param path 日志存储路径
         @param logSize 日志大小
         @param backupCount，备份数（备份大小与日志大小一致，即上参数）
         @param 日志默认级别，默认为info
        */
        UTLOG_EXPORT  std::shared_ptr<UTLogger> createLogger(const std::string& path =""
                , const std::string& filename = ""
                , int logSize = 20 * 1024 * 1024
                , int backupCount = 3
                , utlog::log_level::level_enum l = utlog::log_level::info);

        /**
         * @brief 创建“按日期切分”的logger（每天生成一个新文件）
         * @param path 日志目录
         * @param filename 日志基础文件名（不含扩展名）
         * @param maxDays 保留天数（超过将自动删除更早的日志文件）
         * @param l 日志级别，默认为info
         * 说明：为避免歧义，推荐新代码使用该接口。旧接口 createLogger 仍可用。
         */
        UTLOG_EXPORT  std::shared_ptr<UTLogger> createDailyLogger(const std::string& path =""
                , const std::string& filename = ""
                , int maxDays = 7
                , utlog::log_level::level_enum l = utlog::log_level::info);

        /**
         <AUTHOR> 2021/05/06
         @brief 全局loger实例
        */
        UTLOG_EXPORT std::shared_ptr<UTLogger> getDefaultLogger();

        /**
         <AUTHOR> 2021/05/06
         @brief 在终端打印日志
         @param data、日志信息
         @param 日志级别，默认为debug
        */
        UTLOG_EXPORT bool console(const std::string& data, utlog::log_level::level_enum = utlog::log_level::debug);

        /**
         <AUTHOR> 2021/05/06
         @brief 在终端打印日志
         @param data、日志信息
         @param 日志级别，默认为debug
        */
        UTLOG_EXPORT bool console(const std::string& data, UTLog_Trace_Info traceInfo, utlog::log_level::level_enum = utlog::log_level::debug);

        /**
         <AUTHOR>
         @brief 日志开关
         @param 开关标志
        */
        UTLOG_EXPORT bool setLogEnable(bool logFlag);

        /**
         <AUTHOR> 2021/05/06
         @brief 缓式存入开关
         @param 开关标志
        */
        UTLOG_EXPORT bool setFlushInterval(unsigned seconds);

        /**
         * @brief 转换成枚举日志级别
         * @param logLevel 日志级别
         * @return
         */
        UTLOG_EXPORT utlog::log_level::level_enum toLevelEnum(int logLevel);
}

/**
 <AUTHOR> 2021/05/06
 @brief 日志宏，各级别日志写入及打印等操作，接口名可知,使用宏之前需要先設置defaultlogger
 @paramdata、日志信息
*/
#define UTLOG_FUNCTION static_cast<const char *>(__FUNCTION__)
#define UTLOG_SOURCE_LOCATION utlog::UTLog_Trace_Info{__FILE__, __LINE__, UTLOG_FUNCTION}
#define UTLOG_SET_DEFAULTLOGGER(d_logger) utlog::setDefultLogger(d_logger)
#define UTLOG_INFO(data) utlog::getDefaultLogger()->log(data, UTLOG_SOURCE_LOCATION, utlog::log_level::info)
#define UTLOG_DEBUG(data) utlog::getDefaultLogger()->log(data,UTLOG_SOURCE_LOCATION, utlog::log_level::debug)
#define UTLOG_TRACE(data) utlog::getDefaultLogger()->log(data, UTLOG_SOURCE_LOCATION, utlog::log_level::trace)
#define UTLOG_WARN(data) utlog::getDefaultLogger()->log(data, UTLOG_SOURCE_LOCATION, utlog::log_level::warn)
#define UTLOG_ERROR(data) utlog::getDefaultLogger()->log(data, UTLOG_SOURCE_LOCATION, utlog::log_level::err)
#define UTLOG_CRITICAL(data) utlog::getDefaultLogger()->log(data, UTLOG_SOURCE_LOCATION, utlog::log_level::critical)
#define UTLOG_SET_LOG_ENABLE(logFlag) utlog::setLogEnable(logFlag)
#define UTLOG_SET_TRACE_ENABLE(traceFlag)  utlog::setTraceEnable(traceFlag)
#define UTLOG_SET_CONSOLE_ENABLE(consoleFlag)  utlog::setConsoleEnable(consoleFlag)
#define UTLOG_CONSOLE(data) utlog::console(data, UTLOG_SOURCE_LOCATION)
#define UTLOG_SETFLUSH_INTERVAL(seconds) utlog::setFlushInterval(seconds)
#define UTDEBUG (*utlog::getDefaultLogger().get())
#define UTLOG_SET_LOGPATH(path) \
{\
   UTLOG_SET_DEFAULTLOGGER(utlog::createDailyLogger(path));\
}
#define UTLOG_SET_LEVEL(tlevel) utlog::getDefaultLogger()->setLevel(tlevel)

#endif // LOGHELPER_H
