﻿#ifndef __UTUTIL_FUNCTIONAL_H__
#define __UTUTIL_FUNCTIONAL_H__
#include "ututil_tools.h"
#include <functional>

DEF_BEG_UTUTIL

/**
 * @brief 一元函数对象
 */
template<class Result, class Arg>
struct one_function
{
public:
	//! 默认函数置为空
	one_function() :_fun(NULL) {}

	//! 重载构造函数赋值
	one_function(Result(*fun)(Arg)) :_fun(fun) {}

	//! 重载调用运算符
	Result operator() (Arg arg) {
		if (_fun) return (*_fun)(arg);

		return Result();
	}

	//! 重载函数给内部函数指针赋值
	one_function<Result, Arg>& operator= (Result(*fun)(Arg))
	{
		_fun = fun;
		return *this;
	}

	//! 判断函数指针是否已经赋值
	operator bool() {
		return (_fun != NULL);
	}
private:
	Result(*_fun)(Arg);
};

/**
 * @brief 二元函数对象
 */
template<class Result, class Arg1, class Arg2>
struct tow_function
{
	//! 默认函数置为空
	tow_function() :_fun(NULL) {}

	//! 重载构造函数赋值
	tow_function(Result(*fun)(Arg1, Arg2)) :_fun(fun) {}

	//! 重载调用运算符
	Result operator() (Arg1 a1, Arg2 a2) {
		if (_fun) return (*_fun)(a1, a2);

		return Result();
	}

	//! 重载函数给内部函数指针赋值
	tow_function<Result, Arg1, Arg2>& operator= (Result(*fun)(Arg1, Arg2))
	{
		 _fun = fun;
		return *this;
	}

	//! 判断函数指针是否已经赋值
	operator bool() {
		return (_fun != NULL);
	}
private:
	Result(*_fun)(Arg1, Arg2);
};

/**
 * @brief 三元函数对象
 */
template<class  Result, class Arg1, class Arg2, class Arg3>
struct three_function
{
	//! 默认函数置为空
	three_function() :_fun(NULL) {}

	//! 重载构造函数赋值
	three_function(Result(*fun)(Arg1, Arg2, Arg3)) :_fun(fun) {}

	//! 重载调用运算符
	Result operator() (Arg1 a1, Arg2 a2, Arg3 a3) {
		if (_fun) return (*_fun)(a1, a2, a3);

		return Result();
	}

	//! 重载函数给内部函数指针赋值
	three_function<Result, Arg1, Arg2, Arg3>& operator= (Result(*fun)(Arg1, Arg2, Arg3))
	{
		 _fun = fun;
		return *this;
	}

	//! 判断函数指针是否已经赋值
	operator bool() {
		return (_fun != NULL);
	}
private:
	Result(*_fun)(Arg1, Arg2, Arg3);
};

DEF_END_UTUTIL

#endif
