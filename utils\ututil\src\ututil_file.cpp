﻿#include <algorithm>
#include <string.h>
#include "ututil_string.h"
#include "ututil_file.h"
#include "ututil_algorithm.h"
#include "ututil_sharedptr.h"
#include "ututil_shareapi.h"

DEF_BEG_UTUTIL

//====================CFile==============================
CFile::CFile()
{
     _hfDes = 0;
    //m_sError = NULL;
}

CFile::~CFile()
{
    //if(_sError) {delete []m_sError; _sError = NULL;}
    if(_hfDes)
    {
        ShareApi::CloseFile(_hfDes);
         _hfDes = 0;
    }
}

CFile::CFile(const TCHAR* sPath,unsigned int mode)
{
    //m_sError = NULL;
     _hfDes = 0;
    _Open(sPath, mode);
    //if(!_Open(sPath,mode))
    //{
    //	const TCHAR * sErr = File::GetLastError();
    //		throw sErr;
    //}
}

bool CFile::IsOpen() const
{
    return (_hfDes == 0?false:true);
}

bool CFile::Open(const TCHAR* sPath,unsigned int mode)
{
    return _Open(sPath, mode);
}

inline bool CFile::_Open(const TCHAR* sPath, unsigned int mode)
{
    return (_hfDes = ShareApi::OpenFile(sPath, mode)) == 0 ? false : true;
}

fsize_t CFile::Read(void *buf, fsize_t size)
{
    if(!_hfDes) return -1;

    return (int)ShareApi::ReadFile(_hfDes, buf, size);
}

fsize_t CFile::Write(const void *buf, fsize_t size)
{
    if(!_hfDes) return -1;

    return (int)ShareApi::WriteFile(_hfDes, buf, size);
}

bool CFile::Seek(bigint offset,unsigned int origin)
{
    if(!_hfDes) return false;

    return ShareApi::SetFilePointerEx(_hfDes, offset, origin)?true:false;
}

fsize_t CFile::CurPos() const
{
    if(!_hfDes) return -1;

    return ShareApi::GetFilePointer(_hfDes);
}

bigint CFile::CurPosEx() const
{
    if (!_hfDes) return -1;

    return ShareApi::GetFilePointerEx(_hfDes);
}

const TCHAR *CFile::GetLastError() const
{
    sharedptr<TCHAR> ec(ShareApi::Global_GetLastError(), &ShareApi::Global_FreeMem);
    tcscpy_safe(const_cast<CFile*>(this)->_errMsg, sizeof(_errMsg), ec.Get());
    return _errMsg;
}

bool CFile::Eof() const
{
    if(!_hfDes) return false;

    bigint iPoint,iFileSize;
    // 获取文件大小
    iFileSize = GetFileLength();
    // 获取当前位置
    iPoint = CurPos();

    if(iFileSize == iPoint) return true;
    return false;
}

bigint CFile::GetFileLengthEx() const
{
    if(!_hfDes) return -1;

    return ShareApi::GetFileSizeEx(_hfDes);
}

fsize_t CFile::GetFileLength() const
{
    if (!_hfDes) return -1;

    return ShareApi::GetFileSize(_hfDes);
}

void CFile::Close()
{
    if(_hfDes){
        ShareApi::CloseFile(_hfDes);
         _hfDes = 0;
    }
}

//====================CFileUtil==============================

int CFileUtil::chmod(const char *path, mode_t mode)
{
    //带时区时间
#ifdef WIN32
    return ::_chmod(path, mode);
#else
    return ::chmod(path, mode);
#endif
}


FILE * CFileUtil::fopen(const char * path, const char *  mode)
{
#ifdef WIN32
    FILE *fp;
    if (fopen_s(&fp, path, mode) != 0)
    {
        return NULL;
    }

    return fp;
#else
    return ::fopen(path, mode);
#endif
}

int CFileUtil::lstat(const char * path, stat_t * buf)
{
#ifdef WIN32
    return ::_stat(path, buf);
#else
    return ::lstat(path, buf);
#endif
}

ifstream::pos_type CFileUtil::getFileSize(const std::string &sFullFileName)
{
    ifstream ifs(sFullFileName.c_str());
    ifs.seekg(0, ios_base::end);
    return ifs.tellg();
}

bool CFileUtil::isAbsolute(const std::string &sFullFileName)
{
    if(sFullFileName.empty())
    {
        return false;
    }

    unsigned i = 0;
    while(isspace(sFullFileName[i]))
    {
        ++i;
    }
#ifdef WIN32

    if (sFullFileName.length() >= i + 2)
    {
        if (isPanfu(sFullFileName.substr(i, 2)))
        {
            return true;
        }
    }
    return false;
#else
    return sFullFileName[i] == FILE_SEP[0];
#endif
}


bool CFileUtil::isExist(const std::string &sFullName)
{
    struct stat st;
    return CFileUtil::lstat(sFullName.c_str(), &st) == 0;
}

bool CFileUtil::isDirExist(const std::string &sFullDirName)
{
    return isFileExist(sFullDirName, S_IFDIR);
}

bool CFileUtil::isFileExist(const std::string &sFullFileName, mode_t iFileType)
{
    stat_t f_stat;
    if (CFileUtil::lstat(sFullFileName.c_str(), &f_stat) == -1)
    {
        return false;
    }

    if (!(f_stat.st_mode & iFileType))
    {
        return false;
    }
    return true;
}

bool CFileUtil::isFileExistEx(const std::string &sFullFileName, mode_t iFileType)
{
    struct stat f_stat;
    if (stat(sFullFileName.c_str(), &f_stat) == -1)
    {
        return false;
    }

    if (!(f_stat.st_mode & iFileType))
    {
        return false;
    }

    return true;
}


bool CFileUtil::makeDir(const std::string &sDirectoryPath)
{
    int iRetCode = CFileUtil::mkdir(sDirectoryPath.c_str());

    if(iRetCode < 0 && errno == EEXIST)
    {
        return isFileExistEx(sDirectoryPath, S_IFDIR);
    }
    return iRetCode == 0;
}

bool CFileUtil::makeDirRecursive(const std::string &sDirectoryPath)
{
    std::string simple = simplifyDirectory(sDirectoryPath);

    std::string::size_type pos = 0;
    for(; pos != std::string::npos; )
    {
        pos = simple.find(FILE_SEP, pos + 1);
        std::string s;
        if(pos == std::string::npos)
        {
            s = simple.substr(0, simple.size());
#ifdef WIN32
            if (isPanfu(s))
            {
                return false;
            }
#endif
            return makeDir(s.c_str());
        }
        else
        {
            s = simple.substr(0, pos);
#ifdef WIN32
            if (isPanfu(s))
            {
                continue;
            }
#endif
            if(!makeDir(s.c_str())) return false;
        }
    }
    return true;
}

std::string CFileUtil::simplifyDirectory(const std::string& path)
{
    std::string result = path;

#ifdef WIN32
    result = CStringUtil::replace(result, "/", "\\");
#else
    result = CStringUtil::replace(result, "\\", "/");
#endif

    std::string sep(FILE_SEP);

    std::string::size_type pos;

    pos = 0;
    while((pos = result.find(sep + FILE_SEP, pos)) != std::string::npos)
    {
        result.erase(pos, 1);
    }

    pos = 0;
    while((pos = result.find(sep+ "." + FILE_SEP, pos)) != std::string::npos)
    {
        result.erase(pos, 2);
    }

    while(result.substr(0, 4) == sep + ".." + FILE_SEP)
    {
        result.erase(0, 3);
    }

    if(result.find(sep + ".." + FILE_SEP) != std::string::npos)
    {
        bool ab = CFileUtil::isAbsolute(result);

        std::vector<std::string> dirs = CStringUtil::sepstr<std::string>(result, FILE_SEP);
        std::stack<std::string> q;
        for(size_t i = 0; i < dirs.size(); i++)
        {
            if(dirs[i] == ".." && !q.empty())
            {
                if(!CFileUtil::startWindowsPanfu(q.top()) && q.top() != ".." && q.top() != ".")
                    q.pop();
                else
                {
                    q.push(dirs[i]);
                }
            }
            else
            {
                q.push(dirs[i]);
            }
        }

        result = "";

        while(!q.empty())
        {
            result = q.top() + FILE_SEP + result;
            q.pop();
        }

#ifdef LINUX
        if(ab)
        {
            result = FILE_SEP + result;
        }
#endif
    }

    if(result == sep + ".")
    {
       return result.substr(0, result.size() - 1);
    }

    if(result.size() >= 2 && result.substr(result.size() - 2, 2) == sep + ".")
    {
        result.erase(result.size() - 2, 2);
    }

    if(result == FILE_SEP)
    {
        return result;
    }

    if(result.size() >= 1 && result[result.size() - 1] == FILE_SEP[0])
    {
        result.erase(result.size() - 1);
    }

    if(result == sep + "..")
    {
        result = FILE_SEP;
    }

    return result;
}

int CFileUtil::remove(const std::string &sFullFileName, bool bRecursive)
{
    std::string path = simplifyDirectory(sFullFileName);

    if(isDirExist(path))
    {
        if(bRecursive)
        {
            vector<std::string> files;
            CFileUtil::listDirectory(path, files, false);
            for(size_t i = 0; i < files.size(); i++)
            {
                CFileUtil::remove(files[i], bRecursive);
            }

            if(path != FILE_SEP)
            {
                if(CFileUtil::rmdir(path.c_str()) == -1)
                {
                    return -1;
                }
                return 0;
            }
        }
        else
        {
            if(CFileUtil::rmdir(path.c_str()) == -1)
            {
                return -1;
            }
        }
    }
    else
    {
        if(::remove(path.c_str()) == -1)
        {
            return -1;
        }
    }

    return 0;
}

int CFileUtil::rename(const std::string &sSrcFullFileName, const std::string &sDstFullFileName)
{
    return ::rename(sSrcFullFileName.c_str(), sDstFullFileName.c_str());
}

void CFileUtil::save2file(const std::string &sFullFileName, const std::string &sFileData)
{
    save2file(sFullFileName, sFileData.c_str(), sFileData.length());
}

int CFileUtil::save2file(const std::string &sFullFileName, const char *sFileData, size_t length)
{
    FILE *fp = CFileUtil::fopen(sFullFileName.c_str(), "wb");
    if (fp == NULL)
    {
        return -1;
    }

    size_t ret = fwrite((void*)sFileData, 1, length, fp);
    fclose(fp);

    if(ret == length)
    {
        return 0;
    }
    return -1;
}


void CFileUtil::copyFile(const std::string &sExistFile, const std::string &sNewFile,bool bRemove)
{
    if(CFileUtil::isDirExist(sExistFile))
    {
        CFileUtil::makeDir(sNewFile);
        vector<std::string> tf;
        CFileUtil::listDirectory(sExistFile,tf, false);
        for(size_t i = 0; i <tf.size(); i++)
        {
            std::string fileName = CFileUtil::extractFileName(tf[i]);
            if(fileName == "." || fileName == "..")
                continue;
            std::string s = sExistFile + FILE_SEP + fileName;
            std::string d = sNewFile + FILE_SEP + fileName;
            copyFile(s, d,bRemove);
        }
    }
    else
    {
        if(bRemove) std::remove(sNewFile.c_str());

        std::ifstream fin(sExistFile.c_str(), ios::binary);
        if(!fin)
        {
            //THROW_EXCEPTION_SYSCODE(TC_File_Exception, "[TC_File::copyFile] error: "+sExistFile);
        }
        std::ofstream fout(sNewFile.c_str(), ios::binary);
        if(!fout )
        {
            //THROW_EXCEPTION_SYSCODE(TC_File_Exception, "[TC_File::copyFile] error: "+sNewFile);
        }

        fout << fin.rdbuf();
        fin.close();
        fout.close();

        stat_t f_stat;
        if (CFileUtil::lstat(sExistFile.c_str(), &f_stat) == -1)
        {
            //THROW_EXCEPTION_SYSCODE(TC_File_Exception, "[TC_File::copyFile] error: "+sExistFile);
        }

        chmod(sNewFile.c_str(),f_stat.st_mode);
    }
}


void CFileUtil::listDirectory(const std::string &path, vector<std::string> &files, bool bRecursive)
{
#ifdef LINUX
    std::vector<std::string> tf;
    scanDir(path, tf, 0, 0);

    for(size_t i = 0; i < tf.size(); i++)
    {
        if(tf[i] == "." || tf[i] == "..")
            continue;

        std::string s = path + FILE_SEP + tf[i];

        if(isDirExist(s))
        {
            files.push_back(simplifyDirectory(s));
            if(bRecursive)
            {
                listDirectory(s, files, bRecursive);
            }
        }
        else
        {
            files.push_back(simplifyDirectory(s));
        }
    }
#elif PLATFORM_WIN32
    intptr_t hFile;
    _finddata_t fileinfo;
    if ((hFile = _findfirst(std::string(path + "\\*.*").c_str(), &fileinfo)) != -1)
    {
        do
        {
            std::string sName = fileinfo.name;
            if (sName == "." || sName == "..")
                continue;

            std::string s = path + FILE_SEP + sName;

            if (fileinfo.attrib & _A_SUBDIR)
            {
                files.push_back(simplifyDirectory(s));
                if (bRecursive)
                {
                    listDirectory(s, files, bRecursive);
                }
            }
            else
            {
                files.push_back(simplifyDirectory(s));
            }
        } while (_findnext(hFile, &fileinfo) == 0);
        _findclose(hFile);
    }
#endif
}

bool CFileUtil::startWindowsPanfu(const std::string & sPath)
{
    if (sPath.length() < 2)
    {
        return false;
    }

    char c = sPath[0];

    return isalpha(c) && (sPath[1] == ':');
}

bool CFileUtil::isPanfu(const std::string & sPath)
{
    if (sPath.length() != 2)
    {
        return false;
    }

    char c = sPath[0];

    return isalpha(c) && (sPath[1] == ':');
}



std::string CFileUtil::extractFileName(const std::string &sFullFileName)
{
    if(sFullFileName.length() <= 0)
    {
        return "";
    }

    std::string::size_type found = sFullFileName.find_last_of("/\\");

    // std::string::size_type pos = sFullFileName.rfind(FILE_SEP);
    if(found == std::string::npos)
    {
        return sFullFileName;
    }

    return sFullFileName.substr(found + 1);
}

std::string CFileUtil::extractFilePath(const std::string &sFullFileName)
{
// #if TARGET_PLATFORM_WINDOWS
//     std::string sFullFileNameTmp = TC_Common::replace(sFullFileName, "/", "\\");
// #else
//     std::string sFullFileNameTmp = TC_Common::replace(sFullFileName, "\\", "/");
// #endif

    if (sFullFileName.length() <= 0)
    {
        return std::string(".") + FILE_SEP;
    }

    std::string::size_type found = sFullFileName.find_last_of("/\\");
    if(found == std::string::npos)
    {
        return std::string(".") + FILE_SEP;
    }

    return sFullFileName.substr(0, found+1);

    // std::string::size_type pos = 0;

    // for (pos = sFullFileNameTmp.length(); pos != 0; --pos)
    // {
    //     if (sFullFileNameTmp[pos - 1] == FILE_SEP[0])
    //     {
    //         return sFullFileNameTmp.substr(0, pos);
    //     }
    // }

    // return std::string(".") + FILE_SEP;
}

std::string CFileUtil::extractFileExt(const std::string &sFullFileName)
{
    std::string::size_type found = sFullFileName.find_last_of("/\\");
    if(found == std::string::npos)
    {
        if ((found = sFullFileName.rfind('.')) == std::string::npos)
        {
            return std::string("");
        }

        return sFullFileName.substr(found+1);
    }
    else
    {
        for(std::string::size_type i = sFullFileName.size()-1; i > found; i--)
        {
            if(sFullFileName[i] == '.')
            {
                return sFullFileName.substr(i+1);
            }
        }

        return "";
    }
}

std::string CFileUtil::excludeFileExt(const std::string &sFullFileName)
{
    std::string::size_type found = sFullFileName.find_last_of("./\\");
    if(found != std::string::npos)
    {
        if(sFullFileName[found] == '.')
        {
            return sFullFileName.substr(0, found);
        }
    }

    return sFullFileName;

    // std::string::size_type pos;
    // if ((pos = sFullFileName.rfind('.')) == std::string::npos)
    // {
    //     return sFullFileName;
    // }

    // return sFullFileName.substr(0, pos);
}

std::string CFileUtil::replaceFileExt(const std::string &sFullFileName, const std::string &sExt)
{
    return excludeFileExt(sFullFileName) + "." + sExt;
}


#ifdef LINUX
std::size_t CFileUtil::scanDir(const std::string &sFilePath, std::vector<std::string> &vtMatchFiles, FILE_SELECT f, int iMaxSize )
{
    vtMatchFiles.clear();

    struct dirent **namelist;
    int n = scandir(sFilePath.c_str(), &namelist, f, alphasort);

    if (n < 0)
    {
        return 0;
    }
    else
    {
        while(n-- )
        {
            if(iMaxSize > 0 && vtMatchFiles.size() >= (size_t)iMaxSize )
            {
                free(namelist[n]);
                break;
            }
            else
            {
                vtMatchFiles.push_back(namelist[n]->d_name);
                free(namelist[n]);
            }
        }
        free(namelist);
    }

    return vtMatchFiles.size();
}
#endif


int CFileUtil::mkdir(const char *path)
{
#ifdef WIN32
    int iRetCode = ::_mkdir(path);
#else
    int iRetCode = ::mkdir(path, S_IRUSR | S_IWUSR | S_IXUSR | S_IRGRP | S_IXGRP | S_IROTH | S_IXOTH);
#endif
    return iRetCode;
}

int CFileUtil::rmdir(const char *path)
{
#ifdef WIN32
    return ::_rmdir(path);
#else
    return ::rmdir(path);
#endif
}


DEF_END_UTUTIL

