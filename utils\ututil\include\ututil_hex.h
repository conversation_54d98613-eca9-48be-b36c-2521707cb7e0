﻿#ifndef __UTUTIL_HEX_H
#define __UTUTIL_HEX_H
#include <iostream>
#include <string>
#include <stdarg.h>
#include <string.h>
#include <vector>
#include <sstream>
#include "ututil_tools.h"
 
#include <iomanip>  

DEF_BEG_UTUTIL

using namespace std;

//十六进制数据操作类
class UTUTIL CHexUtil
{
public:
    
    /**
     * @brief 根据16进制的数组转成字符串;如：字节数组"68 00 04 00 08" -> "6800040008"
     * @param data 字节数组
     * @param data 字节数组长度
     * @return 16进制的字符串
     */
#if 0
	static const char DIGITS_UPPER[] = { '0', '1', '2', '3', '4', '5', 
	'6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F' };

    static std::string EncodeToString(const unsigned char *data, int dataLen);
	{
		unsigned char *out = new char[dataLen*2];
		// two characters form the hex value.
		int outIdx =0;
		for (int i = 0; i < dataLen; i++) {
			out[outIdx++] = DIGITS_UPPER[(0xF0 & data[i]) >>> 4];
			out[outIdx++] = DIGITS_UPPER[0x0F & data[i]];
		}
		std::string outStr(out, outIdx);
		delete[] out;
		return outStr;
	}
	static std::string DecodeString(const std::string& d);
#endif
  
	static std::string hexEncode(const unsigned char* data, size_t size);
	
	static std::vector<unsigned char> hexDecode(const std::string& hexStr);
	
};

DEF_END_UTUTIL

#endif // CStringUtil_H
