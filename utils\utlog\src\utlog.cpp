#include "utlogger.h"
#include "utlog.h"
#include "spdlog/spdlog.h"
#include "spdlog/cfg/env.h"
#include <map>
#include "spdlog/sinks/rotating_file_sink.h"
#include "spdlog/sinks/daily_file_sink.h"
#include "spdlog/sinks/stdout_color_sinks.h"
#include "spdlog/fmt/ostr.h"
#include "spdlog/cfg/env.h"
#include <fstream>
#include <memory>
#include <chrono>
#include <ctime>
#include <cstring>
#ifdef WIN32
#include <windows.h>
#include <io.h>
#else
#include <unistd.h>
#include <limits.h>
#endif//win32
#include "ututil_file.h"
#include "ututil_string.h"
//#define SPDLOG_ACTIVE_LEVEL SPDLOG_LEVEL_TRACE

namespace
{
	using spdlog::logger;
	static bool g_ConsoleFlag = false;
	static bool g_LogFlag = true;
	static bool g_TraceFlag = false;
	static std::shared_ptr<utlog::UTLogger> g_Logger =nullptr;
	static std::shared_ptr<spdlog::logger> g_console = spdlog::stdout_color_mt("debugger", spdlog::color_mode::automatic);

#ifdef WIN32
	bool get_executable_path(std::string& processdir, std::string& processname)
	{
		char path[MAX_PATH];
		::GetModuleFileName(NULL, path, MAX_PATH);
		std::string processPath = path;
		auto index = processPath.find_last_of("\\");
		processname = std::string(processPath.begin() + index + 1, processPath.end());
		processdir = std::string(processPath.begin(), processPath.begin() + index);
        printf("log path:%s\\log\\%s.log\n", processdir.c_str(), processname.c_str());
        //int i = 1;
		return true;
	};
#else
	size_t get_executable_path(char* processdir, char* processname, size_t len)
	{
		char* path_end;
		if (readlink("/proc/self/exe", processdir, len) <= 0)
			return -1;
		path_end = strrchr(processdir, '/');
		if (path_end == NULL)
			return -1;
		++path_end;
		strcpy(processname, path_end);
		*path_end = '\0';
		
		return (size_t)(path_end - processdir);
	}
#endif

	bool isDirExists(const std::string& dirPath)
	{
		bool returnFlag = true;
#ifdef WIN32
		if (_access(dirPath.c_str(), 0) == -1)
		{
			returnFlag = (_mkdir(dirPath.c_str()) != -1);
		}
#else
		if (access(dirPath.c_str(), 0) == -1)
		{
			returnFlag = (mkdir(dirPath.c_str(), 0755) != -1);
		}
#endif // WIN32
		return returnFlag;
	}

	bool isFileExists(const std::string& filePath)
	{
		std::ifstream f(filePath.c_str());
		return f.good();
	}
}

namespace utlog
{
	bool console(const std::string& data, utlog::log_level::level_enum l/* = utlog::log_level::debug */)
	{
		if (nullptr != g_console && g_ConsoleFlag)
			g_console->log(static_cast<spdlog::level::level_enum>(l), data);
		return true;
	}

	bool console(const std::string& data, utlog::UTLog_Trace_Info traceInfo, utlog::log_level::level_enum l)
	{
		if (!g_TraceFlag)
			return console(data, l);
		if (nullptr != g_console)
			g_console->log(spdlog::source_loc{ traceInfo.filename, traceInfo.line, traceInfo.funcname }, static_cast<spdlog::level::level_enum>(l), data);
		return true;
	}

	bool setConsoleEnable(bool debugFlag)
	{
		if(debugFlag)
	           g_console->set_level(spdlog::level::trace);
		else
	           g_console->set_level(spdlog::level::warn);
		
		g_ConsoleFlag = debugFlag;
		return true;
	}

	bool setTraceEnable(bool traceFlag)
	{
		g_TraceFlag = traceFlag;
		/*if (traceFlag)
		{
			spdlog::set_level(spdlog::level::trace);
			//spdlog::default_logger()->set_level(spdlog::level::trace);
		}
		else
		{
			spdlog::set_level(spdlog::level::debug);
			//spdlog::default_logger()->set_level(spdlog::level::critical);
		}*/
		return true;
	}

	bool setLogEnable(bool logFlag)
	{
		g_LogFlag = logFlag;
		return true;
	}

	bool setDefultLogger(std::shared_ptr<utlog::UTLogger> defalultLogger)
	{
		g_Logger = defalultLogger;
		return true;
	}

	bool setFlushInterval(unsigned seconds)
	{
		spdlog::flush_every(std::chrono::seconds(seconds));
		return true;
	}

	std::shared_ptr<utlog::UTLogger> createLogger(const std::string& path
		, const std::string& filename
		, int logSize
		, int backupCount
		, utlog::log_level::level_enum l)
	{
		std::shared_ptr<utlog::UTLogger> lptr = nullptr;
		if (!path.empty() && !isDirExists(path))
		{
			printf("%s is not exists, failed to set log path!\n", path.c_str());
			return utlog::getDefaultLogger();
		}
		try
		{
			lptr = std::shared_ptr<utlog::UTLogger>(new RealLogger(path, filename, l, logSize, backupCount));
		}
		catch(std::exception &err)
		{
			printf("Failed to create logger! err: %s", err.what());
		}
		catch (...)
		{
			printf("Failed to create logger! unknown error!\n");
		}

		return lptr;
	}

	std::shared_ptr<utlog::UTLogger> getDefaultLogger()
	{
		if(!g_Logger)
		{
			g_Logger = utlog::createDailyLogger("");
		}
        return g_Logger;
    }

    std::shared_ptr<utlog::UTLogger> createDailyLogger(const std::string& path
        , const std::string& filename
        , int maxDays
        , utlog::log_level::level_enum l)
    {
        std::shared_ptr<utlog::UTLogger> lptr = nullptr;
        if (!path.empty() && !isDirExists(path))
        {
            printf("%s is not exists, failed to set log path!\n", path.c_str());
            return utlog::getDefaultLogger();
        }
        try
        {
            lptr = std::shared_ptr<utlog::UTLogger>(new RealLogger(path, filename, l, maxDays, true));
        }
        catch(std::exception &err)
        {
            printf("Failed to create daily logger! err: %s", err.what());
        }
        catch (...)
        {
            printf("Failed to create daily logger! unknown error!\n");
        }
        return lptr;
    }

    log_level::level_enum toLevelEnum(int logLevel)
    {
        utlog::log_level::level_enum levelEnum;
        switch (logLevel)
        {
            case 0://trace
                levelEnum = utlog::log_level::trace;
                break;
            case 1://debug
                levelEnum = utlog::log_level::debug;
                break;
            case 2://info
                levelEnum = utlog::log_level::info;
                break;
            case 3://warn
                levelEnum = utlog::log_level::warn;
                break;
            case 4://err
                levelEnum = utlog::log_level::err;
                break;
            case 5://critical
                levelEnum = utlog::log_level::critical;
                break;
            default://info
                levelEnum = utlog::log_level::info;
                break;
        }
        return levelEnum;
    }

}

RealLogger::RealLogger(const std::string& path
	, const std::string& filename
	, utlog::log_level::level_enum l
	, int logSize
    , int backupCount): m_logSize(logSize), m_backupCount(backupCount), m_level(l)
{
    m_logDir = "";
    m_logFilePath = "";
    m_loggerName = "";

#ifdef WIN32
    get_executable_path(m_logDir, m_loggerName);
#else
    char path_ch[1024] = "";
    char processname_ch[1024] = "";
    get_executable_path(path_ch, processname_ch, sizeof(path_ch));
    m_logDir = path_ch;
    m_loggerName = processname_ch;
#endif // WIN32
    if (!path.empty())
    {
        m_logDir = path;
    }
    else
    {
        m_logDir = m_logDir +  "/log/" ;
        printf("given path %s is empty, use logDir: %s\n", path.c_str(), m_logDir.c_str());
    }
    if(!filename.empty())
    {
        m_loggerName = filename;
    }

#ifdef WIN32
    m_logDir = ututil::CStringUtil::replace(m_logDir, "/", "\\");
    m_logDir = ututil::CStringUtil::replace(m_logDir, "\\\\", "\\");
    m_logFilePath = m_logDir +"\\"+ m_loggerName + ".log";
    m_logFilePath = ututil::CStringUtil::replace(m_logFilePath, "\\\\", "\\");
#else
    m_logDir = ututil::CStringUtil::replace(m_logDir, "\\", "/");
    m_logDir = ututil::CStringUtil::replace(m_logDir, "//", "/");
    m_logFilePath = m_logDir +"/"+ m_loggerName + ".log";
    m_logFilePath = ututil::CStringUtil::replace(m_logFilePath, "//", "/");
#endif
    m_useDaily = false;
    if(!createLogger())
    {
        return;
    }
    m_console = spdlog::stdout_color_mt(m_loggerName+"_console", spdlog::color_mode::automatic);
    m_console->set_level(static_cast<spdlog::level::level_enum>(l));
    m_console->set_pattern("%+");

	return;
}

RealLogger::~RealLogger()
{
	spdlog::drop(m_Logger->name());
}

// daily 模式构造函数定义
RealLogger::RealLogger(const std::string& path
	, const std::string& filename
	, utlog::log_level::level_enum l
	, int maxDays
	, bool useDaily)
	: m_logSize(0), m_backupCount(maxDays), m_level(l)
{
    m_logDir = "";
    m_logFilePath = "";
    m_loggerName = "";

#ifdef WIN32
    get_executable_path(m_logDir, m_loggerName);
#else
    char path_ch[1024] = "";
    char processname_ch[1024] = "";
    get_executable_path(path_ch, processname_ch, sizeof(path_ch));
    m_logDir = path_ch;
    m_loggerName = processname_ch;
#endif // WIN32
    if (!path.empty())
    {
        m_logDir = path;
    }
    else
    {
        m_logDir = m_logDir +  "/log/" ;
        printf("given path %s is empty, use logDir: %s\n", path.c_str(), m_logDir.c_str());
    }
    if(!filename.empty())
    {
        m_loggerName = filename;
    }

#ifdef WIN32
    m_logDir = ututil::CStringUtil::replace(m_logDir, "/", "\\");
    m_logDir = ututil::CStringUtil::replace(m_logDir, "\\\\", "\\");
    m_logFilePath = m_logDir +"\\"+ m_loggerName + ".log";
    m_logFilePath = ututil::CStringUtil::replace(m_logFilePath, "\\\\", "\\");
#else
    m_logDir = ututil::CStringUtil::replace(m_logDir, "\\", "/");
    m_logDir = ututil::CStringUtil::replace(m_logDir, "//", "/");
    m_logFilePath = m_logDir +"/"+ m_loggerName + ".log";
    m_logFilePath = ututil::CStringUtil::replace(m_logFilePath, "//", "/");
#endif

    m_useDaily = useDaily;
    
    // 如果使用日期模式，更新文件路径以反映实际的日期格式
    if (m_useDaily)
    {
        // 获取当前日期字符串（格式：YYYY-MM-DD）
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        std::tm tm;
        memset(&tm, 0, sizeof(tm));
#ifdef WIN32
        localtime_s(&tm, &time_t);
#else
        localtime_r(&time_t, &tm);
#endif
        char dateStr[32];
        std::strftime(dateStr, sizeof(dateStr), "%Y-%m-%d", &tm);
        
        // 移除原文件名的 .log 后缀，然后添加日期和 .log
        std::string baseFilePath = m_logFilePath;
        if (baseFilePath.size() >= 4 && baseFilePath.substr(baseFilePath.size() - 4) == ".log")
        {
            baseFilePath = baseFilePath.substr(0, baseFilePath.size() - 4);
        }
        m_logFilePath = baseFilePath + "_" + dateStr + ".log";
    }
    
    if(!createLogger())
    {
        return;
    }
    m_console = spdlog::stdout_color_mt(m_loggerName+"_console", spdlog::color_mode::automatic);
    m_console->set_level(static_cast<spdlog::level::level_enum>(l));
    m_console->set_pattern("%+");
}

//创建日志对象
bool RealLogger::createLogger()
{
    printf("createLogger: logDir:%s, logpath: %s\n", m_logDir.c_str(), m_logFilePath.c_str());
    if(!ututil::CFileUtil::isDirExist(m_logDir))
    {
        ututil::CFileUtil::makeDirRecursive(m_logDir);//递归创建目录
    }
    auto t_logger = spdlog::get(m_loggerName);
    if (nullptr != t_logger)
    {
        spdlog::drop(m_loggerName);
    }
    if (m_useDaily)
    {
        auto sink = std::make_shared<spdlog::sinks::daily_file_sink_mt>(m_logFilePath, false, static_cast<uint16_t>(m_backupCount));
        m_Logger = std::make_shared<spdlog::logger>(m_loggerName, sink);
    }
    else
    {
        m_Logger = spdlog::rotating_logger_mt(m_loggerName, m_logFilePath, m_logSize, m_backupCount);
    }
    if (nullptr == m_Logger)
    {
        printf("Failed to create logger! %s\n", m_logFilePath.c_str());
        return false;
    }
    m_Logger->set_level(static_cast<spdlog::level::level_enum>(m_level));
    m_Logger->set_pattern("%+");
    printf("Succeed in creating logger!\n" );
    return true;
}

bool RealLogger::log(const std::string& data, utlog::log_level::level_enum l/* = utlog::log_level::info */)
{
    if(g_LogFlag)
    {
        writeLog(spdlog::source_loc{}, data, l);
    }
    if(g_ConsoleFlag && nullptr != m_console)
    {
        m_console->log(static_cast<spdlog::level::level_enum>(l), data);
    }
	return true;
}

bool RealLogger::log(const std::string & data, utlog::UTLog_Trace_Info traceInfo, utlog::log_level::level_enum l)
{    
    if(g_TraceFlag)
    {
        char fileFunc[255]={0};
        snprintf(fileFunc, sizeof(fileFunc), "%s:%s", traceInfo.filename, traceInfo.funcname);
        spdlog::source_loc traceData = spdlog::source_loc{ fileFunc, traceInfo.line, traceInfo.funcname };
        if(g_LogFlag)
        {
            writeLog(traceData, data, l);
        }
        if (g_ConsoleFlag && nullptr != m_console)
        {
            m_console->log(traceData, static_cast<spdlog::level::level_enum>(l), data);
        }
    }
    else
    {
        log(data, l);
    }
    return true;
}

void RealLogger::writeLog(spdlog::source_loc loc, const std::string &data, utlog::log_level::level_enum l)
{
    if (nullptr != m_Logger)
    {
        if(!loc.empty())
        {
            m_Logger->log(loc, static_cast<spdlog::level::level_enum>(l), data);
        }
        else
        {
            m_Logger->log(static_cast<spdlog::level::level_enum>(l), data);
        }
    }

    //chenfuqing 20220901: 修复：当日志文件被删除后，无法再写入任何日志问题
    //方案：1、当日志写入超过1000条时，检查一下日志文件是否已删除，如是删除则重新创建一个日志对象
    //     2、当日志写入距离上次超过60秒，检查一下日志文件是否已删除，如是删除则重新创建一个日志对象
    m_logNum++;
    if(m_logNum > 1000 || m_elapsedTimer.elapsedSec() > 60)
    {
        m_logNum = 0;
        m_elapsedTimer.reset();
        
        // 获取当前应检查的日志文件路径
        std::string currentLogPath = m_logFilePath;
        if (m_useDaily)
        {
            // 重新生成当前日期的文件路径
            auto now = std::chrono::system_clock::now();
            auto time_t = std::chrono::system_clock::to_time_t(now);
            std::tm tm;
            memset(&tm, 0, sizeof(tm));
#ifdef WIN32
            localtime_s(&tm, &time_t);
#else
            localtime_r(&time_t, &tm);
#endif
            char dateStr[32];
            std::strftime(dateStr, sizeof(dateStr), "%Y-%m-%d", &tm);
            
            // 从基础路径生成当前日期的文件路径
            std::string baseFilePath = currentLogPath;
            size_t lastUndercore = baseFilePath.find_last_of('_');
            if (lastUndercore != std::string::npos)
            {
                size_t dotPos = baseFilePath.find(".log", lastUndercore);
                if (dotPos != std::string::npos)
                {
                    baseFilePath = baseFilePath.substr(0, lastUndercore);
                }
            }
            else if (baseFilePath.size() >= 4 && baseFilePath.substr(baseFilePath.size() - 4) == ".log")
            {
                baseFilePath = baseFilePath.substr(0, baseFilePath.size() - 4);
            }
            currentLogPath = baseFilePath + "_" + dateStr + ".log";
        }
        
        //检查日志文件是否存在, 不存在则重新创建一个日志对象
        if(!isFileExists(currentLogPath))
        {
            printf("Log path '%s' is not exist! Try to create new logger!\n", currentLogPath.c_str());
            createLogger();
        }
    }
    //end chenfuqing 20220901
}

bool RealLogger::setLevel(utlog::log_level::level_enum l)
{
    m_level = l;
    if (nullptr != m_Logger)
    {
        m_Logger->set_level(static_cast<spdlog::level::level_enum>(l));
    }
    if (nullptr != m_console)
    {
        m_console->set_level(static_cast<spdlog::level::level_enum>(l));
    }
    return true;
}
