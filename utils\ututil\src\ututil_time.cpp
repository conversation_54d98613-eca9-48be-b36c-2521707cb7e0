﻿#include "ututil_shareapi.h"
#include "ututil_time.h"
#include "ututil_string.h"
#ifdef LINUX
#	include <sys/time.h>
#endif

USE_UTUTIL;

/** 
 * @brief 获取当前日期时间字符串精确到毫秒(yyyy-mm-dd hh:mm:ss.mmm),如：2021-04-19 14:12:30.154
 * @param chDate 日期之间的分割符
 * @param chBoth 日期和时间之间的分割符
 * @param chTime 时间之间的分割符
 * @param chMill 时间与毫秒之间的分割符
 * @return 返回格式化后的字符串
 * @remarks 如果某个参数传入为0，则不使用分割符(yyyy-mm-dd hh:mm:ss.mmm)
 */
std::string CTimeUtil::getDateTimeStr(int chDate /* = '-' */, int chBoth /* = ' ' */, int chTime /* = ':' */, int chMill /* = '.' */)
{
	std::string str;
	str.reserve(40);
	std::string strTime;
	DateTime tinfo;
	getTimeInfo(&tinfo);
	// 年
	strTime = STD_MOVE(CStringUtil::format_s(8, "%4d", (int)tinfo.nYear));
	str += strTime;
	if (chDate) str += chDate;
	// 月
	strTime = STD_MOVE(CStringUtil::format_s(4, "%02d", (int)tinfo.nMonth));
	str += strTime;
	if (chDate) str += chDate;
	// 日
	strTime = STD_MOVE(CStringUtil::format_s(4, "%02d", (int)tinfo.nDay));
	str += strTime;
	if (chDate) str += chBoth;
	// 时
	strTime = STD_MOVE(CStringUtil::format_s(4, "%02d", (int)tinfo.nHour));
	str += strTime;
	if (chTime) str += chTime;
	// 分
	strTime = STD_MOVE(CStringUtil::format_s(4, "%02d", (int)tinfo.nMinute));
	str += strTime;
	if (chTime) str += chTime;
	// 秒
	strTime = STD_MOVE(CStringUtil::format_s(4, "%02d", (int)tinfo.nSecond));
	str += strTime;
	if (chMill) str += chMill;
	// 毫秒
	strTime = STD_MOVE(CStringUtil::format_s(4, "%03d", (int)tinfo.nMillisecond));
	str += strTime;

	return STD_MOVE(str);
}

/** 
 * @brief 获取当前日期字符串(年-月-日)如：2021-04-19
 * @param chDate 日期之间的分割符
 * @return 返回格式化后的字符串日期
 * @remarks 如果某个参数传入为0，则不使用分割符(yyyy-mm-dd)
*/
std::string CTimeUtil::getDateStr(int chDate /* = '-' */)
{
	std::string str;
	str.reserve(15);
	std::string strTime;
	DateTime tinfo;
	getTimeInfo(&tinfo);

	// 年
	strTime = STD_MOVE(CStringUtil::format_s(8, "%4d", tinfo.nYear));
	str += strTime;
	if (chDate) str += chDate;
	// 月
	strTime = STD_MOVE(CStringUtil::format_s(4, "%02d", (int)tinfo.nMonth));
	str += strTime;
	if (chDate) str += chDate;
	// 日
	strTime = STD_MOVE(CStringUtil::format_s(4, "%02d", (int)tinfo.nDay));
	str += strTime;

	return str;
}

/** 
 * @brief 获取当前时间字符串(时:分:秒.毫秒)
 * @param chTime 时间之间的分割符
 * @param chMill 时间与毫秒之间的分割符
 * @return 返回格式化后的字符串时间
 * @remarks 如果某个参数传入为0，则不使用分割符(hh-mm-ss.mmm)
*/
std::string CTimeUtil::getTimeStr(int chTime /* = ':' */, int chMill /* = '.' */)
{
	std::string str;
	str.reserve(15);
	std::string strTime;
	DateTime tinfo;
	getTimeInfo(&tinfo);

	// 时
	strTime = STD_MOVE(CStringUtil::format_s(4, "%02d", (int)tinfo.nHour));
	str += strTime;
	if (chTime) str += chTime;
	// 分
	strTime = STD_MOVE(CStringUtil::format_s(4, "%02d", (int)tinfo.nMinute));
	str += strTime;
	if (chTime) str += chTime;
	// 秒
	strTime = STD_MOVE(CStringUtil::format_s(4, "%02d", (int)tinfo.nSecond));
	str += strTime;
	if (chMill) str += chMill;
	// 毫秒
	strTime = STD_MOVE(CStringUtil::format_s(4, "%03d", (int)tinfo.nMillisecond));
	str += strTime;

	return STD_MOVE(str);
}

//! 获取当前时间信息
tm* CTimeUtil::getCTimeInfo(tm* tinfo)
{
	time_t t = time(NULL);
#ifdef WIN32
	localtime_s(tinfo, &t);
#else
	localtime_r(&t, tinfo);
#endif
	return tinfo;
}


//! 获取当前时间信息
PDateTime CTimeUtil::getTimeInfo(PDateTime tinfo)
{
#ifdef WIN32
	SYSTEMTIME t;
	GetLocalTime(&t);
	tinfo->nYear = t.wYear;
	tinfo->nMonth = t.wMonth;
	tinfo->nDay = t.wDay;
	tinfo->nHour = t.wHour;
	tinfo->nMinute = t.wMinute;
	tinfo->nSecond = t.wSecond;
	tinfo->nMillisecond = t.wMilliseconds;
#else
	timeval t;
	tm tTmp;
	gettimeofday(&t, NULL);
	localtime_r(&t.tv_sec, &tTmp);
	tinfo->nYear = tTmp.tm_year + 1900;
	tinfo->nMonth = tTmp.tm_mon + 1;
	tinfo->nDay = tTmp.tm_mday;
	tinfo->nHour = tTmp.tm_hour;
	tinfo->nMinute = tTmp.tm_min;
	tinfo->nSecond = tTmp.tm_sec;
	tinfo->nMillisecond = (short)(t.tv_usec / 1000);
#endif

	return tinfo;
}

/**
 * @brief 从当前或者传入的时间值中获取当前日期值(年月日),如：20190523
 * @param t 传入的当前时间值 (time函数获取)
 * @return 返回日期值
 * @remarks 若t为null，则函数自动获取当前的时间
 */
long CTimeUtil::getDateValue(time_t *t)
{
	struct tm tinfo;
	memset(&tinfo, 0, sizeof(tinfo));
	time_t ti = 0;
	if (t == NullPtr) {
		ti = time(NullPtr);
	}
	else {
		ti = *t;
	}
#ifdef WIN32
	localtime_s(&tinfo, &ti);
#else
	localtime_r(&ti, &tinfo);
#endif
	long date = (tinfo.tm_year + 1900) * 10000 + (tinfo.tm_mon + 1) * 100 + tinfo.tm_mday;

	return date;
}

/**
 * @brief 获取当前日期时间数值(年月日),如：20190523
 * @return 返回日期值 格式参考:20190523
 * @remarks 若t为null，则函数自动获取当前的时间
 */
long CTimeUtil::getCurDateValue()
{
    DateTime dateTime;
    CTimeUtil::getTimeInfo(&dateTime);
	return dateTime.nYear * 10000 + dateTime.nMonth * 100 + dateTime.nDay;
}

/**
 * @brief 获取当前日期时间数值(年月日时分秒),如：20190523161230
 * @return 返回日期值 
 * @remarks 若t为null，则函数自动获取当前的时间
 */
UINT64 CTimeUtil::getCurDateTimeValue()
{
    DateTime dateTime;
    CTimeUtil::getTimeInfo(&dateTime);
	return (UINT64)dateTime.nYear * 10000000000 + (UINT64)dateTime.nMonth * 100000000 + (UINT64)dateTime.nDay * 1000000
        + (UINT64)dateTime.nHour * 10000 + (UINT64)dateTime.nMinute * 100+ (UINT64)dateTime.nSecond;
}

/**
 * @brief 获取当前日期时间数值(年月日时分秒毫秒),20190523161230230
 * @return 返回日期值
 * @remarks 若t为null，则函数自动获取当前的时间
 */
UINT64 CTimeUtil::getCurDateTimeMsValue()
{
    DateTime dateTime;
    CTimeUtil::getTimeInfo(&dateTime);
	return (UINT64)dateTime.nYear * 10000000000000 + (UINT64)dateTime.nMonth * 100000000000 + (UINT64)dateTime.nDay * 1000000000
        + (UINT64)dateTime.nHour * 10000000 + (UINT64)dateTime.nMinute * 100000 + (UINT64)dateTime.nSecond * 1000 + (UINT64)dateTime.nMillisecond;
}

