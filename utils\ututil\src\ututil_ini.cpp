﻿#include "ututil_ini.h"
#include "../third/simpleini/simpleini.h"
#include <fcntl.h>
#include <fstream>
#include "ututil_file.h"

USE_UTUTIL;

static const bool IS_EQUAL_SPACE = true;//是否在key=value中间等号前后设置空格如： key = value
static const bool IS_UTF8 = true;//是否设置为utf-8格式

CIniUtil::CIniUtil()
{
}

CIniUtil::~CIniUtil()
{
}

/**
*获取指定段落，键的字符串值
*@param  section  段落名
*@param  key      键名
*@param  defaultVal 找不对应键时的默认返回值
*@return 指定段落，键的值
*/
std::string CIniUtil::getString(const std::string &iniPath, const std::string &section, const std::string &key, const std::string &defaultVal)
{
    if(!CFileUtil::isExist(iniPath))
    {
        printf("[Info][getString] Ini file ' %s' is not exist!\n",iniPath.c_str());

        //配置文件不存在，则生成并将默认值保存到ini文件,并返回默认值
        bool ret = setString(iniPath, section, key, defaultVal);
        if(!ret){
            printf("[Error][getString: %s=%s] Failed to set default value to ini file: %s \n", key.c_str(), defaultVal.c_str(),iniPath.c_str());
        }
        return defaultVal;
    }
    CSimpleIniA ini;
    ini.SetUnicode(IS_UTF8);//设置为utf-8格式
    SI_Error rc;
    rc = ini.LoadFile(iniPath.c_str());
    if (rc < SI_OK)
    {
        printf("[Error][getString] Failed to parse ini file: %s \n",iniPath.c_str());
        return defaultVal;
    }
    if(!ini.KeyExists(section.c_str(), key.c_str()))
    {
        printf("[Info][getString] Key '%s' is not exist in ini file ' %s'!\n", key.c_str(), iniPath.c_str());
        //配置文件中存在此key时，则创建
        bool ret = setString(iniPath, section, key, defaultVal);
        if(!ret){
            printf("[Error][getString: %s=%s] Failed to set default value to ini file: %s \n", key.c_str(), defaultVal.c_str(),iniPath.c_str());
        }
        return defaultVal;
    }
    return ini.GetValue(section.c_str(), key.c_str(), defaultVal.c_str());
}


/**
*获取指定段落，键的整型值
*@param  section  段落名
*@param  key      键名
*@param  defaultVal 找不对应键时的默认返回值
*@return 指定段落，键的值
*/
long CIniUtil::getLong(const std::string &iniPath, const std::string &section, const std::string &key, long defaultVal)
{
    if(!CFileUtil::isExist(iniPath))
    {
        printf("[Info][getLong] Ini file ' %s' is not exist!\n",iniPath.c_str());

        //配置文件不存在，则生成并将默认值保存到ini文件,并返回默认值
        bool ret = setLong(iniPath, section, key, defaultVal);
        if(!ret){
            printf("[Error][getLong: %s=%ld] Failed to set default value to ini file: %s \n", key.c_str(), defaultVal,iniPath.c_str());
        }
        return defaultVal;
    }

    CSimpleIniA ini;
    ini.SetUnicode(IS_UTF8);//设置为utf-8格式
    SI_Error rc;
    rc = ini.LoadFile(iniPath.c_str());
    if (rc < SI_OK)
    {
        printf("[Error][getLong] Failed to parse ini file: %s \n",iniPath.c_str());
        return defaultVal;
    }
    if(!ini.KeyExists(section.c_str(), key.c_str()))
    {
        printf("[Info][getLong] Key '%s' is not exist in ini file ' %s'!\n", key.c_str(), iniPath.c_str());
        //配置文件中存在此key时，则创建
        bool ret = setLong(iniPath, section, key, defaultVal);
        if(!ret){
            printf("[Error][getLong: %s=%ld] Failed to set default value to ini file: %s \n", key.c_str(), defaultVal,iniPath.c_str());
        }
        return defaultVal;
    }
    return ini.GetLongValue(section.c_str(), key.c_str(), defaultVal);
}

int CIniUtil::getInt(const string &iniPath, const string &section, const string &key, int defaultVal)
{
    return (int)getLong(iniPath, section, key, defaultVal);
}

/**
*获取指定段落，键的浮点值
*@param  section  段落名
*@param  key      键名
*@param  defaultVal 找不对应键时的默认返回值
*@return 指定段落，键的值
*/
double CIniUtil::getDouble(const std::string &iniPath, const std::string &section, const std::string &key, double defaultVal)
{
    if(!CFileUtil::isExist(iniPath))
    {
        printf("[Info][getDouble] Ini file ' %s' is not exist!\n",iniPath.c_str());

        //配置文件不存在，则生成并将默认值保存到ini文件,并返回默认值
        bool ret = setDouble(iniPath, section, key, defaultVal);
        if(!ret){
            printf("[Error][getDouble: %s=%f] Failed to set default value to ini file: %s \n", key.c_str(), defaultVal,iniPath.c_str());
        }
        return defaultVal;
    }

    CSimpleIniA ini;
    ini.SetUnicode(IS_UTF8);//设置为utf-8格式
    SI_Error rc;
    rc = ini.LoadFile(iniPath.c_str());
    if (rc < SI_OK)
    {
        printf("[Error][getDouble] Failed to parse ini file: %s \n",iniPath.c_str());
        return defaultVal;
    }
    if(!ini.KeyExists(section.c_str(), key.c_str()))
    {
        printf("[Info][getDouble] Key '%s' is not exist in ini file ' %s'!\n", key.c_str(), iniPath.c_str());
        //配置文件中存在此key时，则创建
        bool ret = setDouble(iniPath, section, key, defaultVal);
        if(!ret){
            printf("[Error][getDouble: %s=%f] Failed to set default value to ini file: %s \n", key.c_str(), defaultVal,iniPath.c_str());
        }
        return defaultVal;
    }
    return ini.GetDoubleValue(section.c_str(), key.c_str(), defaultVal);
}

/**
*获取指定段落，键的布尔值(0--false, 1-- true)
*@param  section  段落名
*@param  key      键名
    当匹配到如下字符开头时，返回1(true):
     "t", "y", "on" or "1"
    当匹配到如下字符开头时，返回0(false):
    "f", "n", "of" or "0"
*@param  defaultVal 找不对应键时的默认返回值
*@return 指定段落，键的布尔值
*/
bool CIniUtil::getBool(const std::string &iniPath, const std::string &section, const std::string &key, const bool defaultVal)
{
    if(!CFileUtil::isExist(iniPath))
    {
        printf("[Info][getBool] Ini file ' %s' is not exist!\n",iniPath.c_str());

        //配置文件不存在，则生成并将默认值保存到ini文件,并返回默认值
        bool ret = setBool(iniPath, section, key, defaultVal);
        if(!ret){
            printf("[Error][getBool: %s=%d] Failed to set default value to ini file: %s \n", key.c_str(), defaultVal,iniPath.c_str());
        }
        return defaultVal;
    }

    CSimpleIniA ini;
    ini.SetUnicode(IS_UTF8);//设置为utf-8格式
    SI_Error rc;
    rc = ini.LoadFile(iniPath.c_str());
    if (rc < SI_OK)
    {
        printf("[Error][getBool] Failed to parse ini file: %s \n",iniPath.c_str());
        return defaultVal;
    }
    if(!ini.KeyExists(section.c_str(), key.c_str()))
    {
        printf("[Info][getBool] Key '%s' is not exist in ini file ' %s'!\n", key.c_str(), iniPath.c_str());
        //配置文件中存在此key时，则创建
        bool ret = setBool(iniPath, section, key, defaultVal);
        if(!ret){
            printf("[Error][getBool: %s=%d] Failed to set default value to ini file: %s \n", key.c_str(), defaultVal,iniPath.c_str());
        }
        return defaultVal;
    }
    return ini.GetBoolValue(section.c_str(), key.c_str(), defaultVal);
}


bool CIniUtil::setString(const string &iniPath, const string &section, const string &key, const string &val)
{
    //检查配置文件不存在，则创建一个空文件
    checkIniFile(iniPath);

    CSimpleIniA ini;
    ini.SetSpaces(IS_EQUAL_SPACE);//是否在key=value中间等号前后设置空格如： key = value
    ini.SetUnicode(IS_UTF8);//设置为utf-8格式
    SI_Error rc;
    rc = ini.LoadFile(iniPath.c_str());
    if (rc < SI_OK)
    {
        printf("[Error][setString] Failed to parse ini file: %s! \n",iniPath.c_str());
        return false;
    }
    rc = ini.SetValue(section.c_str(), key.c_str(), val.c_str());
    if (rc < SI_OK)
    {
        printf("[Error][setString: %s=%s] Failed to set string value to ini file: %s! \n", key.c_str(), val.c_str(), iniPath.c_str());
        return false;
    }
    rc = ini.SaveFile(iniPath.c_str());
    if (rc < SI_OK)
    {
        printf("[Error][setString: %s=%s] Failed to save ini file: %s! \n", key.c_str(), val.c_str(), iniPath.c_str());
        return false;
    }
    return true;
}

bool CIniUtil::setLong(const string &iniPath, const string &section, const string &key, long val)
{
    //检查配置文件不存在，则创建一个空文件
    checkIniFile(iniPath);

    CSimpleIniA ini;
    ini.SetSpaces(IS_EQUAL_SPACE);//是否在key=value中间等号前后设置空格如： key = value
    ini.SetUnicode(IS_UTF8);//设置为utf-8格式
    SI_Error rc;
    rc = ini.LoadFile(iniPath.c_str());
    if (rc < SI_OK)
    {
        printf("[Error][setLong] Failed to parse ini file: %s \n",iniPath.c_str());
        return false;
    }
    rc = ini.SetLongValue(section.c_str(), key.c_str(), val);
    if (rc < SI_OK)
    {
        printf("[Error][setLong: %s=%ld] Failed to set long value to ini file: %s! \n", key.c_str(), val, iniPath.c_str());
        return false;
    }
    rc = ini.SaveFile(iniPath.c_str());
    if (rc < SI_OK)
    {
        printf("[Error][setLong: %s=%ld] Failed to save ini file: %s! \n", key.c_str(), val, iniPath.c_str());
        return false;
    }
    return true;
}

bool CIniUtil::setInt(const string &iniPath, const string &section, const string &key, int val)
{
    return setLong(iniPath, section, key, val);
}

bool CIniUtil::setDouble(const string &iniPath, const string &section, const string &key, double val)
{
    //检查配置文件不存在，则创建一个空文件
    checkIniFile(iniPath);

    CSimpleIniA ini;
    ini.SetSpaces(IS_EQUAL_SPACE);//是否在key=value中间等号前后设置空格如： key = value
    ini.SetUnicode(IS_UTF8);//设置为utf-8格式
    SI_Error rc;
    rc = ini.LoadFile(iniPath.c_str());
    if (rc < SI_OK)
    {
        printf("[Error][setDouble] Failed to parse ini file: %s \n",iniPath.c_str());
        return false;
    }
    rc = ini.SetDoubleValue(section.c_str(), key.c_str(), val);
    if (rc < SI_OK)
    {
        printf("[Error][setDouble: %s=%f] Failed to set double value to ini file: %s! \n", key.c_str(), val, iniPath.c_str());
        return false;
    }
    rc = ini.SaveFile(iniPath.c_str());
    if (rc < SI_OK)
    {
        printf("[Error][setDouble: %s=%f] Failed to save ini file: %s! \n", key.c_str(), val, iniPath.c_str());
        return false;
    }
    return true;
}

bool CIniUtil::setBool(const string &iniPath, const string &section, const string &key, bool val)
{
    //检查配置文件不存在，则创建一个空文件
    checkIniFile(iniPath);

    CSimpleIniA ini;
    ini.SetSpaces(IS_EQUAL_SPACE);////是否在key=value中间等号前后设置空格如： key = value
    ini.SetUnicode(IS_UTF8);//设置为utf-8格式
    SI_Error rc;
    rc = ini.LoadFile(iniPath.c_str());
    if (rc < SI_OK)
    {
        printf("[Error][setBool] Failed to parse ini file: %s!\n",iniPath.c_str());
        return false;
    }
    rc = ini.SetBoolValue(section.c_str(), key.c_str(), val);
    if (rc < SI_OK)
    {
        printf("[Error][setBool: %s=%d] Failed to set bool value to ini file: %s!\n", key.c_str(), val, iniPath.c_str());
        return false;
    }
    rc = ini.SaveFile(iniPath.c_str());
    if (rc < SI_OK)
    {
        printf("[Error][setBool: %s=%d] Failed to save ini file: %s! \n", key.c_str(), val, iniPath.c_str());
        return false;
    }
    return true;
}

void CIniUtil::checkIniFile(const string &iniPath)
{
    if(!CFileUtil::isExist(iniPath))
    {
        std::ofstream output(iniPath);
        output.flush();
        output.close();
        printf("[Info] Create a new ini file: %s! \n",iniPath.c_str());
    }
}
