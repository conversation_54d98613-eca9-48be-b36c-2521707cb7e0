﻿#ifndef __UTUTIL_WARNING_H__
#define __UTUTIL_WARNING_H__

/**
 * @name windows解决导出dll的告警信息
 * @{
 */
#if defined(WIN32) && !defined(STATIC_UTUTIL)
#	define EXPORT_SHARED_PTR( dllmacro, ptrtype ) \
		template class dllmacro sharedptr<ptrtype>

#	define EXPORT_ONE_FUNCTION( dllmacro, type1, type2) \
		template struct dllmacro one_function<type1, type2>
#else
#	define EXPORT_SHARED_PTR( dllmacro, ptrtype ) 
#	define EXPORT_ONE_FUNCTION( dllmacro, type1, type2)
#endif
/** @} */
#endif
