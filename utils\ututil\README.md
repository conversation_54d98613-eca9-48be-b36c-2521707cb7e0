# UTUtil - 通用工具库

## 项目概述

UTUtil 是一个功能丰富的 C++ 通用工具库，提供了跨平台的基础工具类和函数。该库包含字符串处理、文件操作、线程管理、网络通信、JSON 处理、时间工具、加密算法等多个模块，旨在简化 C++ 开发中的常见任务。

## 特性

- 🌐 **跨平台**: 支持 Windows 和 Linux 系统
- 🧵 **线程安全**: 提供线程安全的容器和同步原语
- 📁 **文件操作**: 完整的文件和目录操作工具
- 🔗 **网络通信**: 基础的网络操作封装
- 📝 **字符串处理**: 丰富的字符串操作函数
- ⏰ **时间工具**: 时间格式化和计算工具
- 🔐 **加密算法**: MD5、HEX 编码等
- 📊 **JSON 支持**: JSON 数据处理
- 🏭 **设计模式**: 单例工厂、智能指针等
- 🔧 **配置管理**: INI 文件读写

## 项目结构

```
ututil/
├── include/                    # 头文件目录
│   ├── ututil_tools.h         # 基础工具和平台定义
│   ├── ututil_string.h        # 字符串处理工具
│   ├── ututil_file.h          # 文件操作工具
│   ├── ututil_thread.h        # 线程管理
│   ├── ututil_mutex.h         # 互斥锁
│   ├── ututil_time.h          # 时间工具
│   ├── ututil_json.h          # JSON 处理
│   ├── ututil_net.h           # 网络工具
│   ├── ututil_md5.h           # MD5 加密
│   ├── ututil_hex.h           # HEX 编码
│   ├── ututil_ini.h           # INI 配置文件
│   ├── ututil_random.h        # 随机数生成
│   ├── ututil_task.h          # 任务管理
│   ├── ututil_watchdog.h      # 看门狗
│   ├── ututil_safelist.h      # 线程安全列表
│   ├── ututil_safemap.h       # 线程安全映射
│   ├── ututil_safeset.h       # 线程安全集合
│   ├── ututil_sharedptr.h     # 智能指针
│   ├── ututil_single_factory.h # 单例工厂
│   ├── ututil_autolock.h      # 自动锁
│   ├── ututil_elapsed_timer.h # 计时器
│   ├── ututil_event.h         # 事件
│   ├── ututil_functional.h    # 函数式编程
│   ├── ututil_algorithm.h     # 算法
│   ├── ututil_counter.h       # 计数器
│   └── ututil_warning.h       # 警告处理
├── src/                       # 源文件目录
├── third/                     # 第三方库
│   ├── iniparser/            # INI 解析库
│   ├── jsoncpp/              # JSON 处理库
│   ├── randutils/            # 随机数工具
│   └── simpleini/            # 简单 INI 库
└── ututil.pro                # qmake 项目文件
```

## 编译要求

- **编译器**: 支持 C++11 的编译器 (GCC 5+, MSVC 2015+)
- **依赖库**: Qt Core (仅核心模块)
- **平台**: Windows / Linux / ARM64
- **构建工具**: qmake

## 编译方法

```bash
# 设置环境变量 UTSTENV 指向项目根目录
export UTSTENV=/path/to/your/project

# 使用 qmake 编译
qmake ututil.pro
make
```

编译后会在 `$UTSTENV/shlib` 目录下生成动态库文件。

## 快速开始

### 1. 基本使用

```cpp
#include "ututil_string.h"
#include "ututil_file.h"
#include "ututil_time.h"

using namespace ututil;

int main() {
    // 字符串操作
    std::string text = "Hello World";
    std::string upper = CStringUtil::toUpper(text);
    
    // 文件操作
    if (CFileUtil::isFileExists("test.txt")) {
        std::string content = CFileUtil::readFile("test.txt");
    }
    
    // 时间操作
    std::string currentTime = CTimeUtil::getDateTimeStr();
    
    return 0;
}
```

### 2. 线程安全容器

```cpp
#include "ututil_safemap.h"
#include "ututil_thread.h"

using namespace ututil;

int main() {
    // 线程安全的映射容器
    CSafeMap<std::string, int> safeMap;
    
    // 在多线程环境中安全使用
    safeMap.insert("key1", 100);
    int value = safeMap.find("key1");
    
    return 0;
}
```

### 3. JSON 处理

```cpp
#include "ututil_json.h"

using namespace ututil;

int main() {
    // JSON 字符串转对象
    JsonCpp::Value json;
    std::string jsonStr = R"({"name":"test","value":123})";
    
    if (CJsonUtil::stringToJson(jsonStr.c_str(), &json)) {
        std::string name = json["name"].asString();
        int value = json["value"].asInt();
    }
    
    // JSON 对象转字符串
    JsonCpp::Value newJson;
    newJson["status"] = "success";
    std::string result = CJsonUtil::jsonToString(newJson);
    
    return 0;
}
```

## 核心模块详解

### 字符串工具 (ututil_string.h)

提供丰富的字符串处理功能：

```cpp
// 字符串转换
std::string upper = CStringUtil::toUpper("hello");
std::string lower = CStringUtil::toLower("WORLD");

// 字符串分割
std::vector<std::string> parts = CStringUtil::split("a,b,c", ",");

// 字符串格式化
std::string formatted = CStringUtil::toString("Value: %d", 42);

// 字符串修剪
std::string trimmed = CStringUtil::trim("  hello  ");

// 字符串替换
std::string replaced = CStringUtil::replace("hello world", "world", "C++");

// 16进制转换
std::string hex = CStringUtil::toHexString(data, " ");
```

### 文件工具 (ututil_file.h)

完整的文件和目录操作：

```cpp
// 文件检查
bool exists = CFileUtil::isFileExists("path/to/file.txt");
bool isDir = CFileUtil::isDirectory("path/to/dir");

// 文件读写
std::string content = CFileUtil::readFile("input.txt");
bool success = CFileUtil::writeFile("output.txt", content);

// 目录操作
bool created = CFileUtil::createDirectory("new/path");
std::vector<std::string> files = CFileUtil::listFiles("directory");

// 文件信息
INT64 size = CFileUtil::getFileSize("file.txt");
time_t modTime = CFileUtil::getModifyTime("file.txt");
```

### 线程管理 (ututil_thread.h)

简化的线程操作：

```cpp
// 创建线程
CThread thread([](void* arg) -> DWORD {
    // 线程执行的代码
    return 0;
}, nullptr);

// 等待线程结束
DWORD result = thread.join();

// 获取线程信息
ULONG threadId = thread.id();
```

### 时间工具 (ututil_time.h)

时间格式化和计算：

```cpp
// 获取当前时间字符串
std::string now = CTimeUtil::getDateTimeStr();  // 2024-01-01 12:00:00.123
std::string date = CTimeUtil::getDateStr();     // 2024-01-01
std::string time = CTimeUtil::getTimeStr();     // 12:00:00

// 时间转换
time_t timestamp = CTimeUtil::stringToTime("2024-01-01 12:00:00");
std::string timeStr = CTimeUtil::timeToString(timestamp);

// 时间计算
DateTime dt = CTimeUtil::getCurrentDateTime();
```

### 网络工具 (ututil_net.h)

基础网络操作：

```cpp
// 网络初始化
CNetUtil::initNetwork();

// IP 地址操作
bool isValid = CNetUtil::isValidIP("***********");
std::string hostname = CNetUtil::getHostName();

// 网络清理
CNetUtil::cleanupNetwork();
```

### 加密工具

#### MD5 加密 (ututil_md5.h)

```cpp
// 字符串 MD5
std::string md5Hash = CMD5Util::getMD5("hello world");

// 文件 MD5
std::string fileMD5 = CMD5Util::getFileMD5("file.txt");
```

#### HEX 编码 (ututil_hex.h)

```cpp
// 数据转 HEX
std::string hexStr = CHexUtil::toHex(data, length);

// HEX 转数据
std::vector<BYTE> data = CHexUtil::fromHex("48656C6C6F");
```

### 配置管理 (ututil_ini.h)

INI 文件读写：

```cpp
// 读取 INI 文件
CIniUtil ini("config.ini");
std::string value = ini.getString("section", "key", "default");
int number = ini.getInt("section", "number", 0);
bool flag = ini.getBool("section", "flag", false);

// 写入 INI 文件
ini.setString("section", "key", "new_value");
ini.setInt("section", "number", 42);
ini.save();
```

### 线程安全容器

#### 安全列表 (ututil_safelist.h)

```cpp
CSafeList<int> safeList;

// 线程安全操作
safeList.push_back(1);
safeList.push_front(0);
int value = safeList.front();
safeList.pop_front();
```

#### 安全映射 (ututil_safemap.h)

```cpp
CSafeMap<std::string, int> safeMap;

// 线程安全操作
safeMap.insert("key", 100);
int value = safeMap.find("key");
bool exists = safeMap.contains("key");
safeMap.remove("key");
```

#### 安全集合 (ututil_safeset.h)

```cpp
CSafeSet<int> safeSet;

// 线程安全操作
safeSet.insert(42);
bool exists = safeSet.contains(42);
safeSet.remove(42);
```

### 同步原语

#### 互斥锁 (ututil_mutex.h)

```cpp
CMutex mutex;

// 手动锁定
mutex.Lock();
// 临界区代码
mutex.Unlock();

// 自动锁定
{
    CLockGurad guard(&mutex);
    // 临界区代码，离开作用域自动解锁
}
```

#### 自动锁 (ututil_autolock.h)

```cpp
CAutoLock autoLock;

// 自动管理锁的生命周期
{
    CAutoLock::Guard guard(autoLock);
    // 临界区代码
}
```

### 工具类

#### 计时器 (ututil_elapsed_timer.h)

```cpp
CElapsedTimer timer;

timer.start();
// 执行一些操作
timer.stop();

double elapsed = timer.elapsedSeconds();
INT64 elapsedMs = timer.elapsedMilliseconds();
```

#### 随机数生成 (ututil_random.h)

```cpp
// 生成随机整数
int randomInt = CRandomUtil::randomInt(1, 100);

// 生成随机浮点数
double randomDouble = CRandomUtil::randomDouble(0.0, 1.0);

// 生成随机字符串
std::string randomStr = CRandomUtil::randomString(10);
```

#### 看门狗 (ututil_watchdog.h)

```cpp
CWatchdog watchdog(5000);  // 5秒超时

watchdog.start();
// 定期喂狗
watchdog.feed();

// 检查是否超时
if (watchdog.isTimeout()) {
    // 处理超时
}
```

## 设计模式支持

### 单例工厂 (ututil_single_factory.h)

```cpp
// 定义单例类
class MyService {
public:
    void doSomething() { /* ... */ }
};

// 使用单例工厂
auto& service = CSingleFactory<MyService>::getInstance();
service.doSomething();
```

### 智能指针 (ututil_sharedptr.h)

```cpp
// 创建智能指针
CSharedPtr<MyClass> ptr = CSharedPtr<MyClass>::create();

// 使用智能指针
ptr->method();
(*ptr).member = value;

// 自动内存管理
```

## 平台兼容性

### 跨平台宏定义

```cpp
// 平台检测
#ifdef WIN32
    // Windows 特定代码
#elif defined(LINUX)
    // Linux 特定代码
#endif

// 标准库支持
#ifdef STD11
    // C++11 特性
    auto ptr = std::make_unique<MyClass>();
#else
    // 传统 C++ 代码
    MyClass* ptr = new MyClass();
#endif
```

### 类型定义

库提供了跨平台的类型定义：

```cpp
// 基础类型
DWORD value32;      // 32位无符号整数
UINT64 value64;     // 64位无符号整数
BOOL flag;          // 布尔值
TCHAR text[256];    // 字符类型（支持 Unicode）

// 文件类型
FILE_HANDLE handle; // 文件句柄
fsize_t size;       // 文件大小类型

// 线程类型
THREADPTR thread;   // 线程指针
SYNCHANDLE sync;    // 同步对象句柄
```

## 使用示例

### 示例 1: 文件处理应用

```cpp
#include "ututil_file.h"
#include "ututil_string.h"
#include "ututil_md5.h"

using namespace ututil;

void processFiles(const std::string& directory) {
    // 获取目录中的所有文件
    std::vector<std::string> files = CFileUtil::listFiles(directory);

    for (const auto& file : files) {
        // 检查文件扩展名
        if (CStringUtil::endsWith(file, ".txt")) {
            // 读取文件内容
            std::string content = CFileUtil::readFile(file);

            // 计算 MD5
            std::string md5 = CMD5Util::getMD5(content);

            // 输出信息
            printf("File: %s, MD5: %s\n", file.c_str(), md5.c_str());
        }
    }
}
```

### 示例 2: 多线程数据处理

```cpp
#include "ututil_thread.h"
#include "ututil_safemap.h"
#include "ututil_mutex.h"

using namespace ututil;

CSafeMap<int, std::string> resultMap;
CMutex printMutex;

DWORD workerThread(void* arg) {
    int threadId = *static_cast<int*>(arg);

    // 模拟数据处理
    for (int i = 0; i < 10; ++i) {
        std::string result = CStringUtil::toString("Thread %d - Item %d", threadId, i);
        resultMap.insert(threadId * 10 + i, result);

        // 线程安全的输出
        {
            CLockGurad guard(&printMutex);
            printf("Thread %d processed item %d\n", threadId, i);
        }

        // 模拟处理时间
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    return 0;
}

int main() {
    const int threadCount = 4;
    std::vector<CThread> threads;
    std::vector<int> threadIds(threadCount);

    // 创建工作线程
    for (int i = 0; i < threadCount; ++i) {
        threadIds[i] = i;
        threads.emplace_back(workerThread, &threadIds[i]);
    }

    // 等待所有线程完成
    for (auto& thread : threads) {
        thread.join();
    }

    // 输出结果
    printf("Total results: %d\n", resultMap.size());

    return 0;
}
```

### 示例 3: 配置文件管理

```cpp
#include "ututil_ini.h"
#include "ututil_json.h"
#include "ututil_file.h"

using namespace ututil;

class ConfigManager {
private:
    CIniUtil iniConfig;
    JsonCpp::Value jsonConfig;

public:
    ConfigManager(const std::string& iniFile, const std::string& jsonFile)
        : iniConfig(iniFile) {
        // 加载 JSON 配置
        std::string jsonContent = CFileUtil::readFile(jsonFile);
        CJsonUtil::stringToJson(jsonContent.c_str(), &jsonConfig);
    }

    // 从 INI 获取配置
    std::string getServerAddress() {
        return iniConfig.getString("server", "address", "localhost");
    }

    int getServerPort() {
        return iniConfig.getInt("server", "port", 8080);
    }

    // 从 JSON 获取配置
    std::vector<std::string> getDatabaseHosts() {
        std::vector<std::string> hosts;
        if (jsonConfig.isMember("database") && jsonConfig["database"].isMember("hosts")) {
            for (const auto& host : jsonConfig["database"]["hosts"]) {
                hosts.push_back(host.asString());
            }
        }
        return hosts;
    }

    // 保存配置
    void saveConfig() {
        iniConfig.save();

        std::string jsonStr = CJsonUtil::jsonToString(jsonConfig);
        CFileUtil::writeFile("config.json", jsonStr);
    }
};
```

## 最佳实践

### 1. 内存管理

```cpp
// 使用智能指针管理资源
CSharedPtr<MyClass> ptr = CSharedPtr<MyClass>::create();

// 使用 RAII 管理锁
{
    CLockGurad guard(&mutex);
    // 临界区代码
} // 自动解锁
```

### 2. 异常安全

```cpp
try {
    // 可能抛出异常的操作
    std::string content = CFileUtil::readFile("file.txt");
    JsonCpp::Value json;
    if (!CJsonUtil::stringToJson(content.c_str(), &json)) {
        throw std::runtime_error("Invalid JSON format");
    }
} catch (const std::exception& e) {
    // 错误处理
    printf("Error: %s\n", e.what());
}
```

### 3. 性能优化

```cpp
// 使用计时器测量性能
CElapsedTimer timer;
timer.start();

// 执行需要测量的操作
processLargeData();

timer.stop();
printf("Processing took: %.3f seconds\n", timer.elapsedSeconds());
```

## 注意事项

1. **线程安全**: 线程安全容器适用于多线程环境，但单线程环境下使用标准容器性能更好
2. **内存管理**: 合理使用智能指针，避免内存泄漏
3. **异常处理**: 文件操作和网络操作可能抛出异常，需要适当处理
4. **平台差异**: 某些功能在不同平台上可能有差异，注意测试
5. **性能考虑**: 在性能敏感的场景下，选择合适的工具类

## 故障排除

### 常见问题

1. **编译错误**: 检查 C++11 支持和依赖库
2. **链接错误**: 确保正确链接 Qt Core 库
3. **运行时错误**: 检查文件权限和路径
4. **字符编码问题**: 注意 Unicode 和多字节字符处理

### 调试建议

1. 使用计时器定位性能瓶颈
2. 使用线程安全的日志记录调试多线程问题
3. 检查返回值和异常处理
