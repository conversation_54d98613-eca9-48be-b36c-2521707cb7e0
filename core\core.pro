TARGET = core
TEMPLATE = lib
CONFIG += shared plugin

QT += core sql
greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

# 确保 C++11 标准支持
QMAKE_CXXFLAGS += -std=c++11

DESTDIR = $$(UTSTENV)/shlib

OBJECTS_DIR = $$(UTSTENV)/object/core
MOC_DIR = $$(UTSTENV)/moc/core

INCLUDEPATH += $$(UTSTENV)/interfaces \
               $$(UTSTENV)/utils/utlog/include

# 依赖库
LIBS += -L$$(UTSTENV)/shlib \
        -lutlog

DEFINES += CORE_LIBRARY


SOURCES += \
    pluginmanager.cpp \
    datamanager.cpp \
    datamodels.cpp

HEADERS += \
    core_global.h \
    pluginmanager.h \
    datamanager.h \
    datamodels.h

