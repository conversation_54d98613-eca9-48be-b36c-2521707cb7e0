QT += core gui
greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

TEMPLATE = app
TARGET = utsmarttrolley

# 输出目录
DESTDIR = $$(UTSTENV)/exec

# 对象文件目录
OBJECTS_DIR = $$(UTSTENV)/object/main
MOC_DIR = $$(UTSTENV)/moc/main

# 包含路径
INCLUDEPATH += $$(UTSTENV)/interfaces \
               $$(UTSTENV)/core \
               $$(UTSTENV)/utils/utlog/include 

# 依赖库
LIBS += -L$$(UTSTENV)/shlib \
        -lcore \
        -lutlog

# 运行时库路径
QMAKE_RPATHDIR += $$(UTSTENV)/shlib

# 源文件
SOURCES += \
    main.cpp \
    mainwindow.cpp

# 头文件
HEADERS += \
    mainwindow.h

# UI文件
FORMS += \
    mainwindow.ui

# C++11标准支持
win32-msvc* {
    # MSVC不支持std=c++11选项，使用默认设置
} else {
    QMAKE_CXXFLAGS += -std=c++11
}