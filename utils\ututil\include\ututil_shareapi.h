﻿#ifndef __UTUTIL_SHAREAPI_H__
#define __UTUTIL_SHAREAPI_H__
#include "ututil_tools.h"

DEF_BEG_UTUTIL

namespace ShareApi
{
	/*****************************文件操作************************************/
	/** 
	 * @brief 打开文件
	 * @param szFileName 文件名称
	 * @param iMode 打开模式:查看tools.h头文件中定义的宏
	 * @return 成功返回文件句柄，失败返回NULL
	*/
	UTUTIL FILE_HANDLE OpenFile(const TCHAR* szFileName, unsigned int iMode);
	
	//! 关闭一个文件句柄
	UTUTIL void CloseFile(FILE_HANDLE hFile);

	/**
	 * @brief 定位文件指针
	 * @param hFile 文件句柄
	 * @param dwOffset 偏移量
	 * @param iWhence 定位的相对位置；FILE_SEEK_CUR(当前位置)，FILE_SEEK_SET（文件开头），FILE_SEEK_END（文件末尾）
	 * @return 函数失败返回-1值，成功返回>=0的值
	*/
	UTUTIL BOOL SetFilePointerEx(FILE_HANDLE hFile, INT64 dwOffset, int iWhence);

	//! 参考SetFilePointerEx的函数说明
	UTUTIL BOOL SetFilePointer(FILE_HANDLE hFile, int dwOffset, int iWhence);

	//! 返回当前文件指针位置
	UTUTIL INT64 GetFilePointerEx(FILE_HANDLE hFile);

	//! 参考GetFilePointerEx的函数说明
	UTUTIL int   GetFilePointer(FILE_HANDLE hFile);

	/**
	 * @brief 读取文件数据
	 * @param hFile 文件句柄
	 * @param buf 数据缓冲区
	 * @param size 需要读取的大小
	 * @return 函数失败返回负值，成功返回>=0的值
	 */
	UTUTIL fsize_t ReadFile(FILE_HANDLE hFile, void *buf, fsize_t size);

	/** 
	 * @brief 向文件中写入数据
	 * @param hFile 文件句柄
	 * @param buf 数据缓冲区
	 * @param size 需要写入的大小
	 * @return 函数失败返回-1值，成功返回>=0的值
	*/
	UTUTIL fsize_t WriteFile(FILE_HANDLE hFile, const void *buf, fsize_t size);

	/**
	 * @brief 获取文件大小
	 * @param hFile 文件句柄指针
	 * @return 成功返回文件大小，失败返回-1
	 */
	UTUTIL INT64 GetFileSizeEx(FILE_HANDLE hFile);

	//! 参考GetFileSizeEx的说明
	UTUTIL int GetFileSize(FILE_HANDLE hFile);

	/** 
	 * @brief 文件遍历，win32中不用主动加上*.*,文件分割统一用'/'
	 * @param szDir 目录路径
	 * @param pInfo 文件信息
	 * @return 函数失败返回NULL，成功返回一个有效的句柄
	 * @remarks 此函数不支持通配符查找，szDir必须是一个目录
	 */
	UTUTIL FILE_HANDLE FindFirstFile(const TCHAR *szDir, PFILEFINDINFO pInfo);

	/**
	 * @brief 继续遍历目录
	 * @param hFile FindFirstFile返回的句柄
	 * @param pInfo 接受的数据信息
	 * @return 如果存在文件或目录则返回TRUE，不存在或函数失败返回FALSE
	 */
	UTUTIL BOOL FindNextFile(FILE_HANDLE hFile, PFILEFINDINFO pInfo);

	//! 关闭目录遍历
	UTUTIL void FindClose(FILE_HANDLE hFile);

	/**
	 * @brief 添加目录的子目录名称
	 * @param srcdir 源目录
	 * @param subdir 子目录
	 * @remarks 目录分隔符统一用'/'表示,字符串传长度如果超过MAX_PATH则添加失败
	 */
	UTUTIL void PathAppend(TCHAR *srcdir, const TCHAR *subdir);

	/** 
	 * @brief 检查目录是否存在
	 * @param szDir 目录字符串名称
	 * @return 存在返回TRUE，失败返回FALSE
	*/
	UTUTIL BOOL IsDirent(const TCHAR *szDir);

	/**
	 * @brief 检查文件是否存在
	 * @param szFileName 需要检查的文件名称
	 * @return 成功返回TRUE，失败返回FALSE
	*/
	UTUTIL BOOL FilePathExists(const TCHAR *szFileName);

	/**
	 * @brief 获取当前进程目录
	 * @param size 缓存大小
	 * @param szPath 接收路径的缓存
	 * @return 成功返回TRUE，失败返回FALSE
	*/
	UTUTIL BOOL GetCurrentDirectory(DWORD size, TCHAR *szPath);

	/** 
	 * @brief 创建目录
	 * @param szPathName 目录名称路径
	 * @return 成功返回TRUE，失败返回FALSE
	*/
	UTUTIL BOOL CreateDirectory(const TCHAR* szPathName);
	/*****************************文件操作************************************/

	/*******************************error(begin)**********************************/
	//! 返回上一次函数的错误值
	UTUTIL DWORD GetLastError();

	/**
	 * @brief 获取错误信息
	 * @param pError 如果为NULL则会调用GetLastError或errno两个函数，如果不为NULL则使用pError指向的值进行格式化错误信息
	 * @return 成功返回错误信息，失败返回NULL
	 * @remarks 接受的返回值为堆空间内存，需要使用Global_FreeMem进行释放
	 */
	UTUTIL TCHAR* Global_GetLastError(DWORD* pError = NULL);
	
	//! 释放由Global_GetLastErrr函数申请的内存
	UTUTIL void Global_FreeMem(TCHAR* sInfo);
	/*******************************error(end)  **********************************/

	/*****************************字符串处理(start)*******************************/
	/** 
	 * @name 编码转换函数（GBK=>UNICODE）
	 * @{
	 * @param size 目标字符串的大小（以字符为单位）
	 * @param des 目标字符串
	 * @param src 源字符串
	 * @param srcLen 源字符串长度，若为0则取strlen的长度
	 * @return r1:成功返回TRUE失败返回FALSE; r2:返回写入des的大小（以字符为单位，但不包括\0结束符），若失败则返回-1
	 */
	UTUTIL BOOL AS_CPY_WS(size_t size, WCHAR *des, const char *src);
	UTUTIL size_t GbkToUnicode(WCHAR* des, size_t size, const char* src, size_t srcLen = 0);
	/** @} */

	/** 
	 * @name 编码转换函数(UNICODE=>GBK)
	 * @{
	 * @param size 目标字符串的大小(以字符为单位)
	 * @param des 目标字符串
	 * @param src 源字符串
	 * @param srcLen 源字符串长度,若为0则取wcslen的长度
	 * @return r1:成功返回TRUE失败返回FALSE; r2:成功返回写入des的大小（以字符为单位，但不包括\0结束符），失败返回-1
	 */
	UTUTIL BOOL WS_CPY_AS(size_t size, char *des, const WCHAR *src);
	UTUTIL size_t UnicodeToGbk(char* des, size_t size, const WCHAR* src, size_t srcLen = 0);
	/** @} */

	/** 
	 * @name 编码转换函数(GBK=>UTF8)
	 * @{
	 * @param size 目标字符串的大小(以字符为单位)
	 * @param des 目标字符串
	 * @param src 源字符串
	 * @param srcLen 源字符串长度，若为0则取strlen(src)的长度
	 * @return r1:成功返回TRUE失败返回FALSE; r2:成功返回写入des的大小（以字符为单位，但不包括\0结束符），失败返回-1
	 */
	UTUTIL BOOL AS_CPY_UTF8(size_t size, char *des, const char *src);
	UTUTIL size_t GbkToUtf8(char* des, size_t size, const char* src, size_t srcLen = 0);
	/** @} */

	/** 
	 * @name 编码转换函数(UNICODE=>UTF8)
	 * @{
	 * @param size 目标字符串的大小（以字符为单位）
	 * @param des 目标字符串
	 * @param src 源字符串
	 * @param srcLen 源字符串长度，若为0则取wcslen(src)的长度
	 * @return r1:成功返回TRUE失败返回FALSE; r2:成功返回写入des的大小（以字符为单位，但不包括\0结束符），失败返回-1
	 */
	UTUTIL BOOL WS_CPY_UTF8(size_t size, char *des, const WCHAR *src);
	UTUTIL size_t UnicodeToUtf8(char* des, size_t size, const WCHAR* src, size_t srcLen = 0);
	/** @} */

	/** 
	 * @name 编码转换函数(UTF8=>UNICODE)
	 * @{
	 * @param size 目标字符串的大小（以字符为单位）
	 * @param des 目标字符串
	 * @param src 源字符串
	 * @param srcLen 源字符串长度，若为0则取strlen(src)的长度
	 * @return r1:成功返回TRUE失败返回FALSE; r2:成功返回写入des的大小（以字符为单位，但不包括\0结束符），失败返回-1
	 */
	UTUTIL BOOL UTF8_CPY_WS(size_t size, WCHAR *des, const char *src);
	UTUTIL size_t Utf8ToUnicode(WCHAR* des, size_t size, const char* src, size_t srcLen = 0);

	/** 
	 * @name 编码转换函数(UTF8=>GBK)
	 * @{
	 * @param size 目标字符串的字节大小
	 * @param des 目标字符串
	 * @param src 源字符串
	 * @param srcLen 源字符串的长度，若为0则取strlen(src)的长度
	 * @return r1:成功返回TRUE失败返回FALSE; r2:成功返回写入des的字节大小，失败返回-1
	 */
	UTUTIL BOOL UTF8_CPY_AS(size_t size, char *des, const char* src);
	UTUTIL size_t Utf8ToGbk(char* des, size_t size, const char* src, size_t srcLen = 0);
	/** @} */

	/** 
	 * @name 整型与字符串转换
	 * @{
	 * @brief 长整型转换成字符串
	 * @param value  被转换的(无符号)长整型值/浮点值
	 * @param szValue  转换后的字符串
	 * @param size  szValue的内存大小
	 * @param dotcount  保留的小数位数
	 * @return 返回实际格式化后的字符串大小
	 */
	UTUTIL size_t lltoa(INT64 value, char *szValue, size_t size);
	UTUTIL size_t ultoa(UINT64 value, char *szValue, size_t size);
	UTUTIL size_t ltoa(long_t value, char *szValue, size_t size);
	UTUTIL size_t itoa(int value, char *szValue, size_t size);
	UTUTIL size_t ftoa(float value, char *szValue, size_t size, size_t dotcount = 6);
	UTUTIL size_t lftoa(double value, char *szValue, size_t size, size_t dotcount = 6);
	UTUTIL size_t llftoa(long double value, char *szValue, size_t size, size_t dotcount = 6);
	/** @} */

	/**
	 * @brief 忽略大小写比较两个字符串,最多比较count个字符
	 * @param str1 字符串1
	 * @param str2 字符串2
	 * @param count 比较的长度
	 * @return 相等返回0;str1大于str2返回1;str1小于str2则返回2
	 * @remarks 该函数是安全的,遇到结束符时则会停止比较
	 */
	UTUTIL size_t strnicmp(const char* str1, const char* str2, size_t count);
	/*****************************字符串处理(end)*******************************/

	/********************** 线程相关(start) ***********************/
	/** 
	 * @brief 创建线程
	 * @param stack_size 函数的栈大小，0则为默认(默认THREAD_STACK_MIN的大小,这也是线程最小的堆栈大小)
	 * @param pFunc 回调函数
	 * @param arg 回调函数中的参数
	 * @param pThreadId 线程返回的ID值
	 * @return 失败返回0值，成功返回一个线程句柄
	 */
	UTUTIL THREADPTR BeginThread(ThreadRoutine pFunc, void* arg, UINT stack_size = 0, ULONG* pThreadId = NULL);

	//! 等待线程结束
	UTUTIL BOOL Thread_Join(THREADPTR thHandle);

	//! 关闭线程对象句柄
	UTUTIL void CloseThread(THREADPTR thHandle);

	/**
	 * @name 锁类函数
	 * @{
	 */
	//! 创建锁
	UTUTIL SYNCHANDLE CreateMutex();
	
	//! 加锁
	UTUTIL void MutexLock(SYNCHANDLE hMutex);
	
	//! 解锁
	UTUTIL void MutexUnlock(SYNCHANDLE hMutex);
	
	//! 销毁锁
	UTUTIL void CloseMutex(SYNCHANDLE hMutex);
	/** @} */
    
	/** 
	 * @brief 创建条件变量
	 * @return 成功返回一个信号句柄，失败返回NULL
	 */
	UTUTIL SYNCHANDLE CreateCondVariable();
	/** 
	 * @brief 唤醒单个线程上的等待的条件变量
	 * @param hCond 条件变量句柄
	 * @return 操作成功返回TRUE，失败返回FALSE
	 */
	UTUTIL BOOL NotifyCondVariable(SYNCHANDLE hCond);
	
	/** 
	 * @brief 唤醒所有线程上的等待的条件变量
	 * @param hCond 条件变量句柄
	 * @return 操作成功返回TRUE，失败返回FALSE
	*/
	UTUTIL BOOL NotifyAllCondVariable(SYNCHANDLE hCond);

	/** 
	 * @brief 等待条件变量
	 * @param hCond 创建的信号句柄指针
	 * @param hMutex 传入的锁句柄
	 * @param dwWaitOut 超时时间，单位毫秒,设置为INFINITE则一直等待下去
	 * @return 返回等待后的状态值:
	 *			WAIT_OBJECT_0 等待成功
	 *			WAIT_TIMEOUT  超时
	 *			WAIT_FAILED   发生错误
	 */
	UTUTIL DWORD WaitConditionVariable(SYNCHANDLE hCond, SYNCHANDLE hMutex, DWORD dwWaitOut);

	/** 
	 * @brief 销毁信号量资源
	 * @param hCond 条件变量句柄
	*/
	UTUTIL void CloseConditionVariable(SYNCHANDLE hCond);
	/** @} */

	
	/**
	 * @name 原子操作,加1操作
	 * @{
	 * @param Addend 数据指针
	 * @return 返回加1操作之前的值
	 */
	UTUTIL INT64 InterlockedIncrement64(INT64 *Addend);
	UTUTIL long InterlockedIncrement(long *Addend);
	UTUTIL int InterlockedIncrement(int *Addend);
	/** @} */

	/** 
	 * @name 原子操作,减1操作
	 * @{
	 * @param Addend 数据指针
	 * @return 返回减1操作之前的值
	*/
	UTUTIL INT64 InterlockedDecrement64(INT64 *Addend);
	UTUTIL long InterlockedDecrement(long *Addend);
	UTUTIL int InterlockedDecrement(int *Addend);
	/** @} */

	/** 
	 * @name 原子操作,加法操作
	 * @{
	 * @param Addend 数据指针
	 * @param Value 被加的值
	 * @return 返回增加操作之前的值
	*/
	UTUTIL long InterlockedExchangeAdd(long *Addend, long Value);
	UTUTIL int InterlockedExchangeAdd(int *Addend, int Value);
	UTUTIL INT64 InterlockedExchangeAdd64(INT64 *Addend, INT64 Value);
	/** @} */

	/** 
	 * @name 原子操作,赋值操作
	 * @param Addend 数据指针
	 * @param Value 交换的值
	 * @return 返回交换操作之前的值
	*/
	UTUTIL INT64 InterlockedExchange64(INT64 *Addend, INT64 value);
	UTUTIL long InterlockedExchange(long *Addend, long value);
	UTUTIL int InterlockedExchange(int *Addend, int value);
	/** @} */

	//! 获取当前线程ID
	UTUTIL ULONG GetCurrentThreadId();

	//! 获取当前进程的ID
	UTUTIL ULONG GetCurrentProcessId();
	/********************** 线程相关(end) ***********************/

	//! 线程休眠函数,单位毫秒(ms)
	UTUTIL void Sleep(DWORD dwSleep);

	/** 
	 * @name 获取从系统启动锁经过的时间(单位毫秒)
	 * @{
	 * @return 返回机器的运行时间
	 */
	UTUTIL DWORD GetTickCount();
	UTUTIL DWORD64 GetTickCount64();
	/** @} */

	//! 获取cup时间频率函数(linux下该函数无效)
	UTUTIL BOOL QueryPerformanceFrequency(LARGE_INTEGER *lpFrequency);

	//! 获取CPU的执行时间
	UTUTIL BOOL QueryPerformanceCounter(LARGE_INTEGER *lpPerformanceCount);

	/**
	 * @brief 打印异常后函数调用堆栈信息
	 * @remark 该函数在Windows下还会输出dump文件；
	 *	linux生成core信息依赖命令ulimit-c unlimited故生成的core功能不再本函数之列；
	 *	该函数需要在进程启动时便调用。
	 */
	UTUTIL void PrintDumpInfo(bool bPrintConsole = false);
	
	/**
	 * @brief 开始一个新进程
	 * @param exename 进程镜像文件路径
	 * @param argv 进程启动参数（以NULL结尾）
	 * @param evp 进程环境块（以NULL结尾）
	 * @return 成功返回TRUE，失败返回FALSE
	 * @remarks 默认传入父进程参数和环境变量
	 */
	UTUTIL BOOL StartProcess(const TCHAR* exename, TCHAR** argv = NullPtr, TCHAR** evp = NullPtr);

	/**
	 * @brief 动态载入dll/so库函数
	 * @param dllName 动态库文件路径
	 * @return 成功返回文件句柄，失败返回NULL
	 */
	UTUTIL UTUTIL_HANDLE LoadDll(const TCHAR* dllName);

	/**
	 * @brief 载入动态库中的导出符号
	 * @param dlHandle 打开的动态库句柄
	 * @param symbol 符号名称
	 * @return 返回导出的符号地址指针
	 */
	UTUTIL void* LoadSymbol(UTUTIL_HANDLE dlHandle, const TCHAR* symbol);

	//! 关闭打开的动态库句柄
	UTUTIL void CloseDll(UTUTIL_HANDLE dlHandle);
	
}

DEF_END_UTUTIL

#endif
