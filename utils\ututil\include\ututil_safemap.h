#ifndef __SAFEMAP_H__
#define __SAFEMAP_H__

#include "ututil_mutex.h"
#include <map>
#include <utility>

DEF_BEG_UTUTIL

/**
 * @brief 线程安全的映射类
 */
template<class Kt, class Vt, class Ft = std::less<Kt> >
class CSafeMap
{
public:
	//! 插入一个元素
	void Insert(const Kt& key, const Vt& value)
	{
		CLockGurad lock(&_mutexMap);
		_mapData.insert(std::make_pair(key, value));
	}

	/**
	 * @brief 删除元素
	 * @param key 查找的键
	 * @return 返回删除的元素个数
	*/
	size_t Erase(const Kt& key)
	{
		CLockGurad lock(&_mutexMap);
		return _mapData.erase(key);
	}

	//! 清空所有元素
	void Clear()
	{
		CLockGurad lock(&_mutexMap);
		_mapData.clear();
	}

	/**
	 * @brief 删除第一个元素元素
	 * @param value 返回的结果值
	 * @return 成功返回true,失败返回false
	*/
	bool Pop(Vt& value)
	{
		CLockGurad lock(&_mutexMap);
		if (_mapData.size() > 0)
		{
			value = _mapData.begin()->second;
			_mapData.erase(_mapData.begin());
			return true;
		}
		return false;
	}

	/**
	 * @brief 查找并返回结果值，之后在清除该元素
	 * @param key 查找的键
	 * @param value 返回的值
	 * @return 如果存在该元素则返回true,不存在则返回false
	*/
	bool Erase(const Kt& key, Vt& value)
	{
		CLockGurad lock(&_mutexMap);
		typename std::map<Kt, Vt, Ft>::iterator it = _mapData.find(key);
		if (it != _mapData.end())
		{
			value = it->second;
			_mapData.erase(key);
			return true;
		}

		return false;
	}

	//! 返回当前大小
	std::size_t Size()
	{
		CLockGurad lock(&_mutexMap);
		return _mapData.size();
	}

	//! 查找键值是否存在
	bool Find(const Kt& key)
	{
		CLockGurad lock(&_mutexMap);
		if (_mapData.find(key) != _mapData.end())
		{
			return true;
		}

		return false;
	}

	/**
	 * @brief 查找相应的key是否存在，并返回value值
	 * @param key 键
	 * @param value 值
	 * @return 存在返回true,不存在返回false
	 */
	bool Find(const Kt& key, Vt& value)
	{
		CLockGurad lock(&_mutexMap);
		typename std::map<Kt, Vt, Ft>::iterator it = _mapData.find(key);
		if (it != _mapData.end())
		{
			value = it->second;
			return true;
		}

		return false;
	}
public:
	std::map<Kt, Vt, Ft> _mapData;
	CMutex _mutexMap;
};

DEF_END_UTUTIL

#endif
