# 智能运载小车控制端界面构建说明

## 项目架构概述

本项目采用"接口与实现分离"的现代化架构设计，基于Qt 4.8.6插件化框架。

### 目录结构
```
$UTSTENV/                          # 项目根目录（环境变量）
├── exec/                          # 可执行程序输出目录
├── shlib/                         # 库文件和插件输出目录
├── interfaces/                    # 纯接口定义（无需编译）
│   └── iplugin.h                  # 简化的插件接口
├── core/                          # 核心组件库
│   ├── core.pro                   # 输出到 $UTSTENV/shlib/
│   ├── core_global.h              # 库导出宏定义
│   └── pluginmanager.h/cpp        # 插件管理器实现
├── plugins/                       # 插件源码目录
│   └── headerareaplugin/          # HeaderAreaPlugin（第一个示例插件）
│       ├── headerareaplugin.pro   # 输出到 $UTSTENV/shlib/
│       ├── headerareaplugin.h/cpp # 插件主类
│       ├── headerareawidget.h/cpp # 插件UI组件
│       └── headerareaplugin.json  # Qt5插件元数据
├── main/                          # 主程序源码
│   ├── main.pro                   # 输出到 $UTSTENV/exec/
│   ├── main.cpp
│   ├── mainwindow.h/cpp           # 主窗口（集成插件管理）
│   └── mainwindow.ui
└── utsmarttrolley.pro            # 主构建文件
```

## Ubuntu环境构建步骤

### 1. 环境准备
```bash
# 安装Qt 4.8.6开发环境
sudo apt-get update
sudo apt-get install qt4-dev-tools libqt4-dev

# 安装必要的系统库
sudo apt-get install build-essential
sudo apt-get install libqt4-network libqt4-serialport

# 验证Qt版本
qmake --version
```

### 2. 设置环境变量
```bash
# 设置项目根目录环境变量
export UTSTENV=/path/to/your/UtSmartTrolley

# 例如：
export UTSTENV=/home/<USER>/UtSmartTrolley

# 添加到 ~/.bashrc 以便永久生效
echo "export UTSTENV=/home/<USER>/UtSmartTrolley" >> ~/.bashrc
```

### 3. 创建输出目录
```bash
# 创建构建输出目录
mkdir -p $UTSTENV/exec
mkdir -p $UTSTENV/shlib

# 验证目录创建
ls -la $UTSTENV/
```

### 4. 构建项目
```bash
# 进入项目根目录
cd $UTSTENV

# 生成Makefile
qmake utsmarttrolley.pro

# 编译项目（使用多核加速）
make -j$(nproc)

# 验证构建结果
ls -la $UTSTENV/exec/     # 查看可执行文件
ls -la $UTSTENV/shlib/    # 查看库文件和插件
```

### 5. 运行程序
```bash
# 配置库路径
export LD_LIBRARY_PATH=$UTSTENV/shlib:$LD_LIBRARY_PATH

# 运行主程序
cd $UTSTENV/exec
./utsmarttrolley
```

## 架构特性

### 1. 接口与实现分离
- `interfaces/` 目录包含纯接口定义，保持API稳定性
- `core/` 库实现核心组件（PluginManager等）
- 清晰的依赖关系和模块边界

### 2. 简化的插件接口
- 移除了不必要的元数据字段（version、description、author）
- 专注于核心功能：pluginName、initialize、cleanup、createWidget
- 可选的设置界面支持

### 3. 统一环境管理
- 使用 `$UTSTENV` 环境变量统一路径管理
- 标准化的输出目录结构：exec/ 和 shlib/
- 简化部署和运行环境配置

### 4. 模块化构建
- 按依赖关系构建：core → plugins → main
- 支持增量编译和并行开发
- 清晰的库依赖关系

## HeaderAreaPlugin演示

第一个示例插件 `HeaderAreaPlugin` 提供：
- 实时时间显示
- 系统状态指示
- 连接状态指示器（带闪烁效果）
- 现代化的深色主题UI

这个插件验证了整个插件框架的可行性，后续插件可以基于相同模式快速开发。

## 故障排除

### 常见问题
1. **插件加载失败**: 检查 `$UTSTENV/shlib/` 目录是否包含插件文件
2. **库路径错误**: 确保 `LD_LIBRARY_PATH` 包含 `$UTSTENV/shlib`
3. **Qt版本不匹配**: 确认使用Qt 4.8.6进行编译

### 调试方法
```bash
# 检查插件文件
file $UTSTENV/shlib/*.so

# 检查库依赖
ldd $UTSTENV/exec/utsmarttrolley

# 查看运行时日志
cd $UTSTENV/exec
./utsmarttrolley 2>&1 | tee runtime.log
```

## 扩展指南

要添加新插件，请参照 `HeaderAreaPlugin` 的实现模式：
1. 在 `plugins/` 下创建新目录
2. 实现 `IPlugin` 接口
3. 配置 `.pro` 文件使用 `$UTSTENV` 环境变量
4. 在主项目文件中添加依赖关系
