﻿#ifndef ____UTUTIL_TOOLS_H__
#define ____UTUTIL_TOOLS_H__

/*****************************************************************************
公用c函数内部使用结构体 
仅dll内部使用的文件	
*****************************************************************************/

#include "ututil_tools.h"
//#include <concrt.h>
//#include <list>

/** 
 * @brief 内部使用结构体
 *	锁结构体
 */
typedef struct _MUTEXINFO
{
#ifdef WIN32 // windows使用临界区
	CRITICAL_SECTION mutex;
#elif defined(LINUX) // linux使用互斥锁
	pthread_mutex_t mutex; // PTHREAD_MUTEX_INITIALIZER
#endif
}MTINFO, *PMTINFO;

/**
 * @brief 内部使用结构体
 *	条件变量结构体
 */
typedef struct _CONDVARINFO_
{
#ifdef WIN32
	CONDITION_VARIABLE cond;// 条件变量
#elif defined(LINUX)
	pthread_cond_t cond;	// 条件变量
	pthread_condattr_t attr;// 条件变量属性
#endif
}CONDVARINFO,*PCONDVARINFO;

#ifdef LINUX

/**
 * @brief 内部使用结构体，已废弃
 */
typedef struct _SIGNALINFO
{
	pthread_condattr_t attr;// 条件变量属性
	pthread_cond_t hSignal;	// 条件变量句柄
	pthread_mutex_t hMutex; // 锁
	BOOL bEvent;			// 是否有信号
	BOOL bAutoSignal; 		// 是否自动重置有信号
	int  nWaitCount;		// 等待计数,是否采用惊群
	DWORD dwErrno; 			// 保留值，后续兼容
	_SIGNALINFO() 
	{
		bEvent = FALSE;
		bAutoSignal = FALSE;
		nWaitCount = 0;
		dwErrno = 0;
	}
	// 安全访问变量值
	BOOL IsEvent();
	BOOL IsAutoSignal();
	void SetEvent(BOOL bValue);
}SGINFO, *PSGINFO;

/** 
 * @brief 内部使用结构体
 *	时间结构体
 */
typedef struct _EVENTFD
{
	int efd;			// 事件ID
	int waitcount;		// 正在等待中的数量
	BOOL bEvent;		// 是否有信号
	BOOL bAuto;			// 是否自动重置信号
	_EVENTFD()
	{
		efd = 0;
		waitcount = 0;
		bEvent = FALSE;
		bAuto = FALSE;
	}
}EVENTFD,*PEVENTFD;


inline BOOL SGINFO::IsAutoSignal()
{
	return this->bAutoSignal;
}

inline BOOL SGINFO::IsEvent()
{
	return this->bEvent;
}

inline void SGINFO::SetEvent(BOOL bValue)
{
	pthread_mutex_lock(&this->hMutex);
	this->bEvent = bValue;
	pthread_mutex_unlock(&this->hMutex);
}

/** 
 * @brief 内部使用结构体
 * 目录结构句柄
 */
typedef struct __DirInfo
{
	DIR* pDir;
	TCHAR szDir[MAX_PATH];
}DirInfo, *PDirInfo;

#endif

//USE_UTUTIL;
#endif
