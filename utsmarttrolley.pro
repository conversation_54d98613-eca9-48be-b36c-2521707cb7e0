TEMPLATE = subdirs
CONFIG += ordered

# 智能运载小车控制端界面主项目文件
# 采用"接口与实现分离"的现代化架构设计
# 基于Qt 4.8.6插件化框架

# 子项目构建顺序（按依赖关系排序）
SUBDIRS += \
    utils \
    core \
    plugins/headerareaplugin \
    main

# 确保依赖关系正确
# interfaces/ 目录为纯接口定义，无需编译
# core库包含PluginManager等核心组件
# 插件依赖core库
# 主程序依赖core库

utils.depends = 
core.depends = utils
plugins/headerareaplugin.depends = core
main.depends = core


# 可选的其他插件模块（后续添加）
# SUBDIRS += \
#     utils \
#     datacollection \
#     plugins/videodisplayplugin \
#     plugins/distanceinfoplugin \
#     plugins/controlareaplugin \
#     plugins/debugsettingsplugin \
#     plugins/businesslogicplugin

# 完整依赖关系（后续扩展时启用）
# utils.depends = 
# core.depends = utils
# datacollection.depends = core
# plugins/videodisplayplugin.depends = core
# plugins/distanceinfoplugin.depends = core
# plugins/controlareaplugin.depends = core
# plugins/debugsettingsplugin.depends = core utils
# plugins/businesslogicplugin.depends = core utils
# main.depends = core datacollection