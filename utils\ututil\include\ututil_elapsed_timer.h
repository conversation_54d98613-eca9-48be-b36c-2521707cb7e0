﻿#ifndef __UTUTIL_ELAPSEDTIMER_H__
#define __UTUTIL_ELAPSEDTIMER_H__
#include <chrono>
#include "ututil_tools.h"

DEF_BEG_UTUTIL

/**
 * @brief 耗时计时器类, 主要用于计算一段程序运行耗时长
 */
class UTUTIL CElapsedTimer
{
public:
    CElapsedTimer();
    
    /**
    * @brief 计时器重置
    */
    void reset();

    /**
     * @brief 计算从计时器开始到当前之间的时间差（毫秒）
     * @return 返回时间差
     */
    INT64 elapsedMsec() const;
    
    /**
     * @brief 计算从计时器开始到当前之间的时间差（微秒）
     * @return 返回时间差
     */
    INT64 elapsedMicro() const;
    
    /**
     * @brief 计算从计时器开始到当前之间的时间差（秒）
     * @return 返回时间差
     */
    INT64 elapsedSec() const;
    
    /**
     * @brief 计算从计时器开始到当前之间的时间差（分）
     * @return 返回时间差
     */
    INT64 elapsedMin() const;
    
    /**
     * @brief 计算从计时器开始到当前之间的时间差（时）
     * @return 返回时间差
     */
    INT64 elapsedHour() const;
private:
    std::chrono::time_point<std::chrono::high_resolution_clock> m_begin;

};

DEF_END_UTUTIL

#endif
