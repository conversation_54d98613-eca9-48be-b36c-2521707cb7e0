﻿#ifndef __UTUTIL_EVENT_H__
#define __UTUTIL_EVENT_H__

#include <thread>             // std::thread
#include <chrono>             // std::chrono::seconds
#include <mutex>              // std::mutex, std::unique_lock
#include <condition_variable> // std::condition_variable, std::cv_status

#include "ututil_tools.h"

DEF_BEG_UTUTIL

// 等待事件的返回枚举值
enum wt_status
{
    wt_normal,	///< 正常返回
    wt_timeout	///< 超时返回
};

/** 
 * @brief 事件类
*/
class UTUTIL CEvent
{
	delete_class_copy_operator(CEvent);
public:
    /* 初始化创建一个事件
	*/
    CEvent(){}
    ~CEvent(){}

    /**
     * @brief 等待事件有信号或超时时才退出
     * @param timeoutMs 等待时长(单位毫秒)
     * @param 返回值参考枚举 @enum wt_status
    */
    wt_status wait(unsigned int timeoutMs)
    {
        std::unique_lock<std::mutex> lock(m_mtx);
        std::cv_status status = m_cv.wait_for(lock, std::chrono::milliseconds(timeoutMs));
        wt_status s = wt_normal;
        if(std::cv_status::timeout == status)
        {
            s = wt_timeout;
        }
        return s;
    }

	/** 
     * @brief 等待事件有信号或超时时才退出
     * @param timeoutMs 等待时长(单位毫秒)
     * @param pred 预置条件函数，返回false时，才阻塞等待
	 * @param 返回值参考枚举 @enum wt_status
	*/
    template<typename _Predicate>
    bool wait(unsigned int timeoutMs, _Predicate pred)
    {
        std::unique_lock<std::mutex> lock(m_mtx);
        bool ret = m_cv.wait_for(lock, std::chrono::milliseconds(timeoutMs), pred);
        return ret;
    }
	
    /**
     * @brief 一直等待事件，直到有信号时退出
     * @return
     */
    void waitForever()
    {
        std::unique_lock<std::mutex> lock(m_mtx);
        m_cv.wait(lock);
    }


	/** 
     * @brief  唤醒其中一个等待事件
	*/
    void notifyOne()
    {
        std::unique_lock<std::mutex> lock(m_mtx);
        m_cv.notify_one();
    }

    /**
     * @brief 唤醒所有等待事件
    */
    void notifyAll()
    {
        std::unique_lock<std::mutex> lock(m_mtx);
        m_cv.notify_all();
    }

private:
    std::condition_variable m_cv;
    std::mutex m_mtx;
};


DEF_END_UTUTIL

#endif
