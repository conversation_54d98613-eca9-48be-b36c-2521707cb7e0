﻿#ifndef __UTUTIL_RANDOMUTIL_H__
#define __UTUTIL_RANDOMUTIL_H__

#include <random>
#include "ututil_tools.h"

DEF_BEG_UTUTIL

/**
 * @brief 随机数工具类
 */
class UTUTIL CRandomUtil
{
public:

    /**
     * @brief 随机生成整形的ID号
     * @return
     */
    static UINT64 generateId();


    /**
     * @brief 生成指定长度的随机16进制字符串（注：目前只实现了Linux环境下功能，windows暂未实现）
     * @param len 指定长度
     */
    static std::string getRandomHexChars(unsigned int len);

    /**
     * Get uuid
     */
    static std::string uuid();

private:
    static void randomBuffer(char* ch, size_t len);
};

DEF_END_UTUTIL

#endif
