﻿#ifndef __UTUTIL_JSON_H
#define __UTUTIL_JSON_H
#include <string.h>
#include <iostream>
#include <memory>
#include "../third/jsoncpp/jsoncpp.h"
#include "ututil_tools.h"

DEF_BEG_UTUTIL

class UTUTIL CJsonUtil
{
public:

    /**
     * @brief Json value转字符串
     * @param json Json value
     * @return
     */
    static std::string jsonToString(JsonCpp::Value &json);

    /**
     * @brief 字符串转Json value
     * @param str
     * @param json
     * @return
     */
    static bool stringToJson(const char *str, JsonCpp::Value *json);

};
DEF_END_UTUTIL

#endif
