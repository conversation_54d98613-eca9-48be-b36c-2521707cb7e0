﻿#ifndef __UTUTIL_TASK_H_
#define __UTUTIL_TASK_H_

#include <list>
#include <deque>
#include <thread>
#include <mutex>
#include <iostream>
#include <condition_variable>
#include <atomic>
#include <memory>

#include "ututil_tools.h"
DEF_BEG_UTUTIL

/**
 * @brief 任务基类
 */
class CTask
{
public:
    /**
     * @brief 任务执行函数
     */
    virtual void run() = 0;

    virtual ~CTask() {}

    void setTaskName(const std::string &taskName)
    {
        m_taskName = taskName;
    }

    std::string getTaskName()
    {
        return m_taskName;
    }
private:
    std::string m_taskName;//任务名
};

/**
 * @brief 任务池
 */
class CTaskPool
{
public:
    CTaskPool(const std::string &poolName, unsigned int poolSize) :m_poolSize(poolSize),
        m_poolName(poolName)
    {
        m_stopedCount.store(0);
        m_stop.store(true);
    }

    CTaskPool(unsigned int poolSize) :m_poolSize(poolSize), m_poolName("TaskThread")
    {
        m_stopedCount.store(0);
        m_stop.store(true);
    }
    virtual ~CTaskPool();

    /**
     * @brief 从对列中获取任务
     * @return 任务对象
     */
    std::shared_ptr<CTask> take();

    /**
     * @brief 往对列中添加任务
     * @param 任务对象
     */
    void put(std::shared_ptr<CTask> t);

    /**
     * @brief 开启任务线程池
     * @param errMsg [返回] 错误消息
     */
    bool start(std::string &errMsg);

    /**
     * @brief 停止任务线程池
     */
    void stop();

private:
    static void runInThread(void *param);

    CTaskPool() :m_poolSize(0), m_poolName("CannotBeCreated")
    {
        m_stopedCount.store(0);
        m_stop.store(true);
    }

    unsigned int m_poolSize;
    std::string m_poolName;
    std::deque<std::shared_ptr<CTask>> m_taskList;
    std::mutex m_mutex;
    std::condition_variable m_notEmptyCv;
    std::condition_variable m_notFullCv;
    std::atomic<bool> m_stop;//停止标志
    std::atomic<unsigned int> m_stopedCount; //已停止的线程数

};


DEF_END_UTUTIL

#endif
