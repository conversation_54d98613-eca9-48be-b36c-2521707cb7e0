# =============== Qt 项目常用忽略规则 ===============

# Qt Creator 配置文件
*.pro.user
*.pro.user.*
*.qbs.user
*.qbs.user.*
*.qmlproject.user
*.qmlproject.user.*

# Qt 设计师生成的临时文件
*.autosave

# 构建目录（根据个人习惯调整，比如 build/ out/ bin/ 等）
build/
build-*/
out/
bin/
obj/

# qmake 产生的文件
Makefile*
*.moc
moc_*.cpp
qrc_*.cpp
ui_*.h
*.qmlc
*.jsc

# CMake 相关
CMakeLists.txt.user
CMakeCache.txt
CMakeFiles/
cmake_install.cmake
CTestTestfile.cmake
*.cmake
*.cbp

# Visual Studio / MSVC 相关
*.sln
*.vcxproj*
*.ncb
*.suo
*.pdb
*.opendb
*.idb
*.aps
*.ilk
*.obj
*.exe
*.dll
*.lib
*.exp

# MinGW / GCC / Clang 相关
*.o
*.a
*.so
*.so.*
*.dylib

# macOS / Xcode 相关
*.DS_Store
*.xcodeproj/
*.xcworkspace/
*.plist
*.app

# Windows 系统文件
Thumbs.db
Desktop.ini

# Qt 安装的备份/临时文件
*.bak
*.swp
*.tmp

# 日志 / 运行时文件
*.log
*.diff
*.patch
*.rej
*.out
*.app/
*.qmake.stash

# =============== 自定义规则 ===============
# 如果你有生成的文档、测试结果，可以加上
doc/
tests/output/
.vs
.vscode