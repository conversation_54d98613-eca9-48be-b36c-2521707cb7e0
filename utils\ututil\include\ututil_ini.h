﻿#ifndef __UTUTIL_INI_H__
#define __UTUTIL_INI_H__
#include <iostream>
#include <string>
#include <mutex>
#include <map>
#include "ututil_tools.h"

using namespace std;

DEF_BEG_UTUTIL

 /**
*配置文件(*.ini)工具类
*ini文件格式如下：
* [SECTION]
* KEY=VALUE
*
*/
class UTUTIL CIniUtil
{
public:
    /**
    *获取配置文件ini工具类单例
    *@param  iniPath  文件（*.ini)全路径
    */
    static CIniUtil* instance()
    {
        static CIniUtil _inst;
        return &_inst;
    }
    
    /**
    *获取指定段落，键的字符串值
    *@param iniPath ini文件路径
    *@param  section  段落名
    *@param  key      键名
    *@param  defaultVal 找不对应键时的默认返回值
    *@return 指定段落，键的值
    */
    std::string getString(const std::string &iniPath, const std::string &section, const std::string &key, const std::string &defaultVal);
    
    /**
    *获取指定段落，键的长整型值
    *@param iniPath ini文件路径
    *@param  section  段落名
    *@param  key      键名
    *@param  defaultVal 找不对应键时的默认返回值
    *@return 指定段落，键的值
    */
    long getLong(const std::string &iniPath, const std::string &section, const std::string &key, const long defaultVal);

    /**
    *获取指定段落，键的整型值
    *@param iniPath ini文件路径
    *@param  section  段落名
    *@param  key      键名
    *@param  defaultVal 找不对应键时的默认返回值
    *@return 指定段落，键的值
    */
    int getInt(const std::string &iniPath, const std::string &section, const std::string &key, const int defaultVal);

    /**
    *获取指定段落，键的浮点值
    *@param iniPath ini文件路径
    *@param  section  段落名
    *@param  key      键名
    *@param  defaultVal 找不对应键时的默认返回值
    *@return 指定段落，键的值
    */
    double getDouble(const std::string &iniPath, const std::string &section, const std::string &key, const double defaultVal);

    /**
    *获取指定段落，键的布尔值(0--false, 1-- true)
    *@param iniPath ini文件路径
    *@param  section  段落名
    *@param  key      键名    
        当匹配到如下字符开头时，返回1(true):
         "t", "y", "on" or "1"
        当匹配到如下字符开头时，返回0(false):
        "f", "n", "of" or "0"
    *@param  defaultVal 找不对应键时的默认返回值
    *@return 指定段落，键的布尔值
    */
    bool getBool(const std::string &iniPath, const std::string &section, const std::string &key, const bool defaultVal);

    /**
     * @brief 设置字符串值
     * @param iniPath ini文件路径
     * @param section 段落名
     * @param key   键名
     * @param val 键的值
     * @return 成功，或失败
     */
    bool setString(const std::string &iniPath, const std::string &section, const std::string &key, const std::string &val);


    /**
     * @brief 设置长整形值
     * @param iniPath ini文件路径
     * @param section 段落名
     * @param key   键名
     * @param val 键的值
     * @return 成功，或失败
     */
    bool setLong(const std::string &iniPath, const std::string &section, const std::string &key, long val);

    /**
     * @brief 设置整形值
     * @param iniPath ini文件路径
     * @param section 段落名
     * @param key   键名
     * @param val 键的值
     * @return 成功，或失败
     */
    bool setInt(const std::string &iniPath, const std::string &section, const std::string &key, int val);

    /**
     * @brief 设置浮点值
     * @param iniPath ini文件路径
     * @param section 段落名
     * @param key   键名
     * @param val 键的值
     * @return 成功，或失败
     */
    bool setDouble(const std::string &iniPath, const std::string &section, const std::string &key, double val);


    /**
     * @brief 设置浮点值
     * @param iniPath ini文件路径
     * @param section 段落名
     * @param key   键名
     * @param val 键的值
     * @return 成功，或失败
     */
    bool setBool(const std::string &iniPath, const std::string &section, const std::string &key, bool val);

private:
    
    CIniUtil();
    ~CIniUtil();

    //检查配置文件不存在，则创建一个空文件
    void checkIniFile(const string &iniPath);
};


DEF_END_UTUTIL

#endif
