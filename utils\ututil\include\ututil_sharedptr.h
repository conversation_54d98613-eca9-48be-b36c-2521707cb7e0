﻿#ifndef __UTUTIL_SHAREDPTR_H__
#define __UTUTIL_SHAREDPTR_H__

#include "ututil_shareapi.h"
//#include "../debug/debug.h"
#include <utility>

DEF_BEG_UTUTIL

class ShareCountBase
{
	int _nUser;
	int _nSelf;
public:
	ShareCountBase() :_nUser(0), _nSelf(0) {}
	virtual ~ShareCountBase(){ }

	virtual void DelUser() = 0;
	virtual void DelSelf() = 0;

	virtual void* GetPtr() = 0;

	int IncRefUser()
	{
        return ShareApi::InterlockedIncrement(&_nUser);
	}

	void DecRefUser()
	{
        if (ShareApi::InterlockedDecrement(&_nUser) == 1)
		{
			DelUser();
		}
	}

	void IncRefSelf()
	{
        ShareApi::InterlockedIncrement(&_nSelf);
	}

	void DecRefSelf()
	{
        if (ShareApi::InterlockedDecrement(&_nSelf) == 1)
		{
			DelSelf();
		}
	}

	int RefCount() const {
		return _nUser;
	}
};

template<class T>
class ShareCount :public ShareCountBase
{
	T *_pData;
public:
	ShareCount(T *pData)
	{
		_pData = pData;
	}
	virtual ~ShareCount(){ }
	virtual void DelUser()
	{
		if (_pData)
		{
			//DPRINTF("delete _pData = %s\n", typeid(T).name());
			delete _pData;
			_pData = NullPtr;
		}
	}
	virtual void DelSelf()
	{
		//DPRINTF("delete this = %s\n", typeid(T).name());
		delete this;
	}
	virtual void* GetPtr()
	{
		return (void*)_pData;
	}
};

template<class T, class DELTYPE>
class ShareCountDel:public ShareCountBase
{
	T *_pData;
	DELTYPE _pFunc;
public:
	ShareCountDel(T* pData, DELTYPE func)
	{
		 _pData = pData;
		 _pFunc = func;
	}
	virtual ~ShareCountDel() {}
	virtual void DelUser()
	{
		if (_pData)
		{
			 _pFunc(_pData);
			 _pData = NullPtr;
		}
	}
	virtual void DelSelf()
	{
		delete this;
	}
	virtual void* GetPtr()
	{
		return (void*)_pData;
	}
};

/**
 * @brief 智能指针
 * @remarks 为没有c++11的编译器写的智能指针
 */
template<class Ty>
class sharedptr
{
public:
	sharedptr() :_pBase(NullPtr) {}

	sharedptr(Ty* p) :_pBase(NullPtr)
	{
		Reset(p);
	}

	template<class DELTYPE>
	sharedptr(Ty* p, DELTYPE pFunc) :_pBase(NullPtr)
	{
		Reset(p, pFunc);
	}

	sharedptr(const sharedptr<Ty>& obj) :_pBase(NullPtr)
	{
		Reset(obj);
	}

	~sharedptr()
	{
		DecRef();
	}

	sharedptr<Ty>& operator= (const sharedptr<Ty>& obj)
	{
		sharedptr<Ty>(obj).Swap(*this);
		return *this;
	}

	operator bool () const
	{
		return (Get() != NullPtr);
	}

#ifdef STD11
	sharedptr(sharedptr<Ty>&& obj) :_pBase(NullPtr)
	{
		Swap(obj);
	}

	sharedptr<Ty>& operator= (sharedptr<Ty>&& obj)
	{
		if (this != &obj)
		{
			Reset(NullPtr);
			Swap(obj);
		}

		return *this;
	}
#endif

public: // 指针访问
	void Reset(Ty* p)
	{
		DecRef();
		if (p != NullPtr)
		{
			_pBase = new ShareCount<Ty>(p);
			IncRef();
		}
		else {
			_pBase = NullPtr;
		}
	}

	template<class DELTYPE>
	void Reset(Ty* p, DELTYPE pFunc)
	{
		DecRef();
		if (p != NullPtr)
		{
			_pBase = new ShareCountDel<Ty, DELTYPE>(p, pFunc);
			IncRef();
		}
		else {
			_pBase = NullPtr;
		}
	}
	
	Ty* Get()
	{
		if (_pBase)
		{
			return (Ty*)_pBase->GetPtr();
		}
		else
		{
			return NullPtr;
		}
	}

	Ty* Get() const
	{
		if (_pBase)
		{
			return (Ty*)_pBase->GetPtr();
		}
		else
		{
			return NullPtr;
		}
	}

	Ty& operator* ()
	{
		return *(Get());
	}

	const Ty& operator* () const
	{
		return *(Get());
	}

	Ty* operator-> () const
	{
		return Get();
	}
	Ty* operator -> ()
	{
		return Get();
	}

	void Swap(sharedptr<Ty>& ptr)
	{
		if (this != &ptr)
			std::swap(_pBase, ptr._pBase);
	}

	// 返回引用次数
	int RefCount() const
	{
		if (_pBase) return _pBase->RefCount();
		return 0;
	}
protected:
	void IncRef() const
	{
		if (_pBase)
		{
			_pBase->IncRefSelf();
			_pBase->IncRefUser();
		}
	}
	void DecRef() const
	{
		if (_pBase)
		{
			_pBase->DecRefUser();
			_pBase->DecRefSelf();
		}
	}

	void Reset(const sharedptr<Ty>& obj)
	{
		if (this != &obj)
		{
			DecRef();
			obj.IncRef();
			_pBase = obj._pBase;
		}
	}
private:

	ShareCountBase *_pBase;
};

template<class Ty1,
class Ty2>
	bool operator==(const sharedptr<Ty1>& _Left,
	const sharedptr<Ty2>& _Right)
{
	return (_Left.Get() == _Right.Get());
}

template<class Ty1,
class Ty2>
	bool operator!=(const sharedptr<Ty1>& _Left,
	const sharedptr<Ty2>& _Right)
{
	return (!(_Left == _Right));
}

template<class Ty1,
class Ty2>
	bool operator<(const sharedptr<Ty1>& _Left,
	const sharedptr<Ty2>& _Right)
{
	return (_Left.Get() < _Right.Get());
}

template<class Ty1,
class Ty2>
	bool operator>=(const sharedptr<Ty1>& _Left,
	const sharedptr<Ty2>& _Right)
{
	return (!(_Left < _Right));
}

template<class Ty1,
class Ty2>
	bool operator>(const sharedptr<Ty1>& _Left,
	const sharedptr<Ty2>& _Right)
{
	return (_Right < _Left);
}

template<class Ty1,
class Ty2>
	bool operator<=(const sharedptr<Ty1>& _Left,
	const sharedptr<Ty2>& _Right)
{
	return (!(_Right < _Left));
}

DEF_END_UTUTIL

#endif
