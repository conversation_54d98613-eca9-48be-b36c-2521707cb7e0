﻿
#ifndef __UTUTIL_COUNTER_H__
#define __UTUTIL_COUNTER_H__
#include "ututil_shareapi.h"

DEF_BEG_UTUTIL

/**
 * @brief 高精度计时器
 */
class UTUTIL CCounter
{
public:
	CCounter() {
		QueryPerformanceFrequency(&_fre);
		Begin(); 
	}
	//! 微秒
	long long NowCSec(){
		LARGE_INTEGER Now;
		::ShareApi::QueryPerformanceCounter(&Now);
		return ((Now.QuadPart-m_StartTime.QuadPart)*1000000) / _fre.QuadPart;
	} 
	//! 毫秒 
	long long NowMSec(){
		LARGE_INTEGER Now;
		::ShareApi::QueryPerformanceCounter(&Now);
		return ((Now.QuadPart-m_StartTime.QuadPart)*1000) / _fre.QuadPart;
	}
protected:
	void Begin(){
		::ShareApi::QueryPerformanceCounter(&_StartTime);
	}		
private:
	LARGE_INTEGER _fre;	     ///< 高精度下计数器的频率
	LARGE_INTEGER _StartTime;	 ///< 高精度下的开始时间值 
}; 

DEF_END_UTUTIL

#endif
