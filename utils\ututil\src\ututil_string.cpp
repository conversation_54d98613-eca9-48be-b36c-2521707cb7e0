﻿
#include "ututil_string.h"
#include "ututil_shareapi.h"
#ifdef WIN32
    #include <windows.h>
#endif


USE_UTUTIL;

#define CODE_TRANSFORM(strtype, codestr, str, func) { \
	size_t rcvSize = func((strtype*)codestr.data(), codestr.size(), str.data(), str.size()); \
	if (rcvSize == (size_t)-1) { \
		codestr.clear(); \
	}\
	else { \
		codestr.erase(codestr.begin() + rcvSize, codestr.end()); \
	}\
}


/**
 * @brief 根据分隔符把字节数组组合成16进制的字符串;如：字节数组 -> "68 00 04 00 08"， 这里分隔符sep是空格
 * @param data 字节数组
 * @param sep 分隔符
 * @return 16进制的字符串
 */
std::string CStringUtil::toHexString(const std::string &data, const std::string &sep)
{
    if (data.size() <=0)
    {
        return std::string("");
    }
    std::string out;
    char byteBuf[5] = {0};
    sprintf(byteBuf, "%02X", (unsigned char)(data.at(0)));
    out += byteBuf;
    std::size_t i = 1;
    for (; i < data.size(); i++)
    {
        memset(byteBuf, 0, 5);
        sprintf(byteBuf, "%s%02X", sep.c_str(), (unsigned char)(data.at(i)));
        out += byteBuf;
    }
    return out;
}


/**
 * @brief 将带格式的字符串转换成std::string
 * @param msgFormat 字符串格式
 * @return std::string字符串
 */
std::string CStringUtil::toString(const char *format, ...)
{
    va_list params;
	va_start(params, format);
    std::basic_string<char> str;
	if (!format) return str;
	str.resize(1024);
	size_t rcvSize = 0;
	do
	{
		// 格式化
		rcvSize = CStringUtil::sprintf_ex((char*)str.data(), str.size(), format, params);
		// 若缓存不够则以2倍的速度增长
		if (rcvSize > str.size())
		{
			str.resize(str.size() * 2);
			continue;
		}
		break;
	} while (true);
	str.erase(str.begin() + rcvSize, str.end());
    va_end(params);
	return STD_MOVE(str);
}
/**
 * @brief 将带格式的字符串转换成std::wstring
 * @param msgFormat 字符串格式
 * @return std::wstring
 */
std::wstring CStringUtil::toString(const WCHAR *format, ...)
{
    va_list params;
	va_start(params, format);
    std::basic_string<WCHAR> str;
	if (!format) return str;
	str.resize(1024);
	size_t rcvSize = 0;
	do
	{
		// 格式化
		rcvSize = CStringUtil::sprintf_ex((WCHAR*)str.data(), str.size(), format, params);
		// 若缓存不够则以2倍的速度增长
		if (rcvSize > str.size())
		{
			str.resize(str.size() * 2);
			continue;
		}
		break;
	} while (true);
	str.erase(str.begin() + rcvSize, str.end());
	va_end(params);
	return STD_MOVE(str);
}

std::string CStringUtil::trim(const std::string &sStr, const std::string &s, bool bChar)
{
    if(sStr.empty())
    {
        return sStr;
    }

    /**
    * 将完全与s相同的字符串去掉
    */
    if(!bChar)
    {
        return trimright(trimleft(sStr, s, false), s, false);
    }
    return trimright(trimleft(sStr, s, true), s, true);
}

std::string CStringUtil::trimleft(const std::string &sStr, const std::string &s, bool bChar)
{
    if(sStr.empty())
    {
        return sStr;
    }

    /**
    * 去掉sStr左边的字符串s
    */
    if(!bChar)
    {
        if(sStr.length() < s.length())
        {
            return sStr;
        }
        if(sStr.compare(0, s.length(), s) == 0)
        {
            return sStr.substr(s.length());
        }
        return sStr;
    }

    /**
    * 去掉sStr左边的 字符串s中的字符
    */
    std::string::size_type pos = 0;
    while(pos < sStr.length())
    {
        if(s.find_first_of(sStr[pos]) == std::string::npos)
        {
            break;
        }
        pos++;
    }
    if(pos == 0) return sStr;
    return sStr.substr(pos);
}

std::string CStringUtil::trimright(const std::string &sStr, const std::string &s, bool bChar)
{
    if(sStr.empty())
    {
        return sStr;
    }

    /**
    * 去掉sStr右边的字符串s
    */
    if(!bChar)
    {
        if(sStr.length() < s.length())
        {
            return sStr;
        }

        if(sStr.compare(sStr.length() - s.length(), s.length(), s) == 0)
        {
            return sStr.substr(0, sStr.length() - s.length());
        }
        return sStr;
    }

    /**
    * 去掉sStr右边的 字符串s中的字符
    */
    std::string::size_type pos = sStr.length();
    while(pos != 0)
    {
        if(s.find_first_of(sStr[pos-1]) == std::string::npos)
        {
            break;
        }
        pos--;
    }
    if(pos == sStr.length()) return sStr;
    return sStr.substr(0, pos);
}

std::string CStringUtil::lower(const std::string &s)
{
    std::string sString = s;
    for (std::string::iterator iter = sString.begin(); iter != sString.end(); ++iter)
    {
        *iter = tolower(*iter);
    }
    return sString;
}

std::string CStringUtil::upper(const std::string &s)
{
    std::string sString = s;
    for (std::string::iterator iter = sString.begin(); iter != sString.end(); ++iter)
    {
        *iter = toupper(*iter);
    }
    return sString;
}

bool CStringUtil::isdigit(const std::string &sInput)
{
    std::string::const_iterator iter = sInput.begin();
    if(sInput.empty())
    {
        return false;
    }
    while(iter != sInput.end())
    {
        if (!::isdigit(*iter))
        {
            return false;
        }
        ++iter;
    }
    return true;
}


/*
* 判断字符串是否以某个字符串开头
* 参数：
*	str -- 待判断字符串
*	substr -- 开头字符串
* 返回：
*	bool 返回比较结果 true表示是以substr开头,false表示不是以substr开头
*/
bool CStringUtil::startsWith(const std::string &str, const std::string &substr)
{
    return str.find(substr) == 0;
}

/*
* 判断字符串是否以某个字符串结尾
* 参数：
*	str -- 待判断字符串
*	substr -- 结尾字符串
* 返回：
*	bool 返回比较结果 true表示是以substr结尾,false表示不是以substr结尾
*/
bool CStringUtil::endsWith(const std::string &str, const std::string &substr)
{
    return str.rfind(substr) == (str.length() - substr.length());
}

/*
* 不区分大小写字符串比较
* 参数：
*	str1 -- 第一个字符串
*	str2 -- 第二个字符串
* 返回：
*	bool 返回比较结果 true表示相等,false表示不相等
*/
bool CStringUtil::equalsIgnoreCase(const std::string &str1, const std::string &str2)
{
    return lower(str1) == lower(str2);
}

template <>
std::string CStringUtil::tostr<bool>(const bool &t)
{
    char buf[2];
    buf[0] = t ? '1' : '0';
    buf[1] = '\0';
    return std::string(buf);
}


template <>
std::string CStringUtil::tostr<char>(const char &t)
{
    char buf[2];
    snprintf(buf, 2, "%c", t);
    return std::string(buf);
}

template <>
std::string CStringUtil::tostr<unsigned char>(const unsigned char &t)
{
    char buf[2];
    snprintf(buf, 2, "%c", t);
    return std::string(buf);
}

template <>
std::string CStringUtil::tostr<short>(const short &t)
{
    char buf[16];
    snprintf(buf, 16, "%d", t);
    return std::string(buf);
}

template <>
std::string CStringUtil::tostr<unsigned short>(const unsigned short &t)
{
    char buf[16];
    snprintf(buf, 16, "%u", t);
    return std::string(buf);
}

template <>
std::string CStringUtil::tostr<int>(const int &t)
{
    char buf[16];
    snprintf(buf, 16, "%d", t);
    return std::string(buf);
}

template <>
std::string CStringUtil::tostr<unsigned int>(const unsigned int &t)
{
    char buf[16];
    snprintf(buf, 16, "%u", t);
    return std::string(buf);
}

template <>
std::string CStringUtil::tostr<long>(const long &t)
{
    char buf[32];
    snprintf(buf, 32, "%ld", t);
    return std::string(buf);
}

template <>
std::string CStringUtil::tostr<long long>(const long long &t)
{
    char buf[32];
    snprintf(buf, 32, "%lld", t);
    return std::string(buf);
}


template <>
std::string CStringUtil::tostr<unsigned long>(const unsigned long &t)
{
    char buf[32];
    snprintf(buf, 32, "%lu", t);
    return std::string(buf);
}

template <>
std::string CStringUtil::tostr<float>(const float &t)
{
    //C++11 to_string，默认保留后面6位小数
    std::string s = std::to_string(t);

    //去掉无效0, eg. 1.0300 -> 1.03;1.00 -> 1
    bool bFlag = false;
    int pos = int(s.size() - 1);
    for(; pos > 0; --pos)
    {
        if(s[pos] == '0')
        {
            bFlag = true;
            if(s[pos-1] == '.')
            {
                //-2为了去掉"."号
                pos -= 2;
                break;
            }
        }
        else
        {
            break;
        }
    }
    if(bFlag)
    {
        s = s.substr(0, pos + 1);
    }
    return s;
}

template <>
std::string CStringUtil::tostr<double>(const double &t)
{
    //C++11 to_string，默认保留后面6位小数
    std::string s = std::to_string(t);
    //去掉无效0, eg. 1.0300 -> 1.03;1.00 -> 1
    bool bFlag = false;
    int pos = int(s.size() - 1);
    for(; pos > 0; --pos)
    {
        if(s[pos] == '0')
        {
            bFlag = true;
            if(s[pos-1] == '.')
            {
                //-2为了去掉"."号
                pos -= 2;
                break;
            }
        }
        else
        {
            break;
        }
    }
    if(bFlag)
    {
        s = s.substr(0, pos + 1);
    }
    return s;
}

template <>
std::string CStringUtil::tostr<long double>(const long double &t)
{
    char buf[32];
    snprintf(buf, 32, "%Lf", t);
    std::string s(buf);

    //去掉无效0, eg. 1.0300 -> 1.03;1.00 -> 1
    bool bFlag = false;
    int pos = int(s.size() - 1);
    for(; pos > 0; --pos)
    {
        if(s[pos] == '0')
        {
            bFlag = true;
            if(s[pos-1] == '.')
            {
                //-2为了去掉"."号
                pos -= 2;
                break;
            }
        }
        else
        {
            break;
        }
    }
    if(bFlag)
    {
        s = s.substr(0, pos + 1);
    }
    return s;
}

template <>
std::string CStringUtil::tostr<std::string>(const std::string &t)
{
    return t;
}

std::string CStringUtil::replace(const std::string &sString, const std::string &sSrc, const std::string &sDest)
{
    if(sSrc.empty())
    {
        return sString;
    }

    string sBuf = sString;

    string::size_type pos = 0;

    while( (pos = sBuf.find(sSrc, pos)) != string::npos)
    {
        sBuf.replace(pos, sSrc.length(), sDest);
        pos += sDest.length();
    }

    return sBuf;
}

std::string CStringUtil::replace(const std::string &sString, const map<string,string>& mSrcDest)
{
    if(sString.empty())
    {
        return sString;
    }

    std::string tmp = sString;
    std::map<string,string>::const_iterator it = mSrcDest.begin();

    while(it != mSrcDest.end())
    {

        std::string::size_type pos = 0;
        while((pos = tmp.find(it->first, pos)) != string::npos)
        {
            tmp.replace(pos, it->first.length(), it->second);
            pos += it->second.length();
        }

        ++it;
    }

    return tmp;
}

bool CStringUtil::matchPeriod(const std::string& s, const std::string& pat)
{
    if(s.empty())
    {
        return false;
    }

    if(pat.empty())
    {
        return true;
    }

    if(pat.find('*') == std::string::npos)
    {
        return s == pat;
    }

    std::string::size_type sIndex = 0;
    std::string::size_type patIndex = 0;
    do
    {
        if(pat[patIndex] == '*')
        {
            if(s[sIndex] == '.')
            {
                return false;
            }

            while(sIndex < s.size() && s[sIndex] != '.')
            {
                ++sIndex;
            }
            patIndex++;
        }
        else
        {
            if(pat[patIndex] != s[sIndex])
            {
                return false;
            }
            ++sIndex;
            ++patIndex;
        }
    }
    while(sIndex < s.size() && patIndex < pat.size());

    return sIndex == s.size() && patIndex == pat.size();
}

bool CStringUtil::matchPeriod(const std::string& s, const std::vector<string>& pat)
{
    for(size_t i = 0; i < pat.size(); i++)
    {
        if(CStringUtil::matchPeriod(s,pat[i]))
        {
            return true;
        }
    }
    return false;
}


std::string CStringUtil::unicodeToGbk(const std::wstring& str)
{
	std::string gbk;
	gbk.resize(str.size() * 2 + 1);
	
	CODE_TRANSFORM(char, gbk, str, ShareApi::UnicodeToGbk);

	return STD_MOVE(gbk);
}

//! gbk转换成unicode编码
std::wstring CStringUtil::gbkToUnicode(const std::string& str)
{
	std::wstring unicode;
	unicode.resize(str.size() + 1);

	CODE_TRANSFORM(WCHAR, unicode, str, ShareApi::GbkToUnicode);

	return STD_MOVE(unicode);
}

//! utf8转换成unicode编码
std::wstring CStringUtil::utf8ToUnicode(const std::string& str)
{
	std::wstring unicode;
	unicode.resize(str.size() + 1);
	CODE_TRANSFORM(WCHAR, unicode, str, ShareApi::Utf8ToUnicode);

	return STD_MOVE(unicode);
}

//! unicode转换成utf8编码
std::string CStringUtil::unicodeToUtf8(const std::wstring& str)
{
	std::string utf8;
	utf8.resize(str.size() * 4 + 1);
	CODE_TRANSFORM(char, utf8, str, ShareApi::UnicodeToUtf8);

	return STD_MOVE(utf8);
}

//! utf8 转换成gbk编码
std::string CStringUtil::utf8ToGbk(const std::string& str)
{
	std::string gbk;
	gbk.resize(str.size() * 2 + 1);
	CODE_TRANSFORM(char, gbk, str, ShareApi::Utf8ToGbk);

	return STD_MOVE(gbk);
}

//! gbk转换成utf8编码
std::string CStringUtil::gbkToUtf8(const std::string& str)
{
	std::string utf8;
	utf8.resize(str.size() * 4 + 1);
	CODE_TRANSFORM(char, utf8, str, ShareApi::GbkToUtf8);

	return STD_MOVE(utf8);
}



std::string CStringUtil::format(const char* mat, ...)
{
	va_list params;
	va_start(params, mat);
	std::string str = inline_format(mat, params);
	va_end(params);

	return STD_MOVE(str);
}

std::wstring CStringUtil::format(const WCHAR* mat, ...)
{
	va_list params;
	va_start(params, mat);
	std::wstring str = inline_format(mat, params);
	va_end(params);

	return STD_MOVE(str);
}

std::string CStringUtil::format_s(size_t bufSize, const char* mat, ...)
{
	va_list params;
	va_start(params, mat);
	std::string str = inline_format_s(bufSize, mat, params);
	va_end(params);

	return STD_MOVE(str);
}

std::wstring  CStringUtil::format_s(size_t bufSize, const WCHAR* mat, ...)
{
	va_list params;
	va_start(params, mat);
	std::wstring str = inline_format_s(bufSize, mat, params);
	va_end(params);

	return STD_MOVE(str);
}

