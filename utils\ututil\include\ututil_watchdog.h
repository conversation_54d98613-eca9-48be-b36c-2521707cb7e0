#ifndef __UTUTIL_WATCHDOG_H_
#define __UTUTIL_WATCHDOG_H_


#include <functional>
#include "ututil_tools.h"

DEF_BEG_UTUTIL

class CWatchDog
{
public:
	CWatchDog();
    virtual ~CWatchDog() {}
    static bool start(const std::function<bool()>& childProcess);

private:
    static void forkChildProcess(int);

private:
    static std::function<bool()> m_childProcess;
};
std::function<bool()> CWatchDog::m_childProcess = nullptr;

DEF_END_UTUTIL

#endif