﻿#ifndef __THREADUTIL_H__
#define __THREADUTIL_H__
#include <string>
#include <chrono>
#include <thread>
#include <exception>
#include "ututil_tools.h"
#include "ututil_functional.h"

DEF_BEG_UTUTIL


EXPORT_ONE_FUNCTION(UTUTIL, DWORD, void*);

/**
 * @brief 线程异常类
 */ 
class CThreadException :public std::exception
{
public:
	CThreadException() {}
	CThreadException(const char* msg) :_msg(msg) {}
	virtual ~CThreadException() throw() {}
	virtual const char* what() const throw() { return _msg.c_str(); }
private:
	std::string _msg;
};

/**
 * @brief 线程类
 */
class UTUTIL CThread
{
	delete_class_copy_operator(CThread);
public:
	//! 默认构造函数只创建一个未运行的线程类，之后运行需要调用Start函数
	CThread();
	//! 调用构造函数并创建线程,如果失败则抛出异常
	CThread(const one_function<DWORD, void*>& func, void* arg = NullPtr);
	~CThread();
	/** 
	 * @brief 等待线程函数的退出
	 * @return 线程函数的返回值
	*/
    DWORD join();

	//! 获取线程ID
    ULONG id();

	//! 获取线程返回值
    DWORD getRet();

	//! 线程的运行状态,返回true则线程正在运行，false则已经退出
    bool isRun();

	//! 分离线程
    void detach();

	/**
	 * @brief 运行一个新的线程
	 * @return 成功返回true,失败返回false
	 * @remarks 如果之前已经有重载的构造函数运行线程，该函数会返回false
	*/
    bool start(const one_function<DWORD, void*>& func, void* arg);
public:
	struct ThreadData
	{
		ThreadData();
		one_function<DWORD, void*> _func;	///< 线程函数
		void * _arg;						///< 线程参数
		THREADPTR _hThread;					///< 线程句柄
		DWORD _dwRet;						///< 线程函数的返回值
		ULONG _id;							///< 线程的id
		bool _bRun;							///< 标志线程是否在运行
		bool _detach;						///< 是否分离
	};
protected:
	static DWORD ThreadFunc(void *arg);
private:
	ThreadData *_data;
};

/**
 * @brief 线程工具类
 */
class UTUTIL CThreadUtil
{
public:
    /** 
	 * @brief 使当前线程休眠一段时间（秒）
	 * @param sec 秒数
	 * @return
	*/
    static void sleep(int sec);
    
    /** 
	 * @brief 使当前线程休眠一段时间（毫秒）
	 * @param ms 毫秒数
	 * @return
	*/
    static void sleepMs(int ms);
};

DEF_END_UTUTIL

#endif // __TIME_H__
