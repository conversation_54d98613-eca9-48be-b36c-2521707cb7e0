﻿#ifndef __UTUTIL_SAFELIST_H__
#define __UTUTIL_SAFELIST_H__
#include "ututil_mutex.h"
#include <list>

DEF_BEG_UTUTIL

/**
 * @brief 线程安全操作列表类
 */ 
template<class T>
class CSafeList
{
public:
	/**
	 * @brief 向链表尾部中插入一个数据
	 * @param value 需要插入的值
	 */
	void InsertBack(const T& value)
	{
		CLockGurad lock(&_mutexList);
		_list.push_back(value);
		++_size;
	}

	/**
	 * @brief 向链表头部中插入一个数据
	 * @param value 需要插入的值
	 */
	void InsertFront(const T& value)
	{
		CLockGurad lock(&_mutexList);
		_list.push_front(value);
		++_size;
	}

	/**
	 * @brief 返回链表的头部数据，并删除
	 * @param value 接收链表返回的数据值
	 * @return 如果有数据则返回true,否则返回false
	*/
	bool Front(T& value)
	{
		CLockGurad lock(&_mutexList);
		if(_list.empty()) return false;

		if(_size > 0) --_size;
		value = _list.front();
		_list.pop_front();
		return true;
	}

	/**
	 * @brief 返回链表的尾部数据，并删除
	 * @param value 接收链表返回的数据值
	 * @return 如果有数据则返回true,否则返回false
	*/
	bool Back(T& value)
	{
		CLockGurad lock(&_mutexList);
		if(_list.empty()) return false;

		if(_size > 0) --_size;
		value = _list.back();
		 _list.pop_back();
		return true;
	}

	/**
	 * @brief 返回链表的大小
	 * @remarks 使用时应当小心，多线程的情况下不能保证其值的实时性
	*/
	size_t Size()
	{
		return _size;
	}

	//! 清空所有元素
	void Clear()
	{
		CLockGurad lock(&_mutexList);
		_list.clear();
		_size = 0;
	}
private:
	CMutex _mutexList;
	std::list<T> _list;
	size_t _size;
};

DEF_END_UTUTIL

#endif // endif __SAFELIST_H__
