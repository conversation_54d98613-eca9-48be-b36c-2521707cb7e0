﻿#ifndef __UTUTIL_SINGLEFACTORY_H__
#define __UTUTIL_SINGLEFACTORY_H__

#include "ututil_mutex.h"
#include "ututil_sharedptr.h"

DEF_BEG_UTUTIL

/**
 * @brief 单例模式工具类
 */ 
template<class T>
class CSingleFactory
{
private:
	static sharedptr<T> g_singlePtr;
	static CMutex g_mutexPtr;
public:
	//! 生成一个单列模式实例
	static T* GetInstance()
	{
		if (g_singlePtr.Get() == NullPtr)
		{
			CLockGurad lk(&g_mutexPtr);
			if (g_singlePtr.Get() == NullPtr) {
				g_singlePtr.Reset(new T());
			}
		}

		return g_singlePtr.Get();
	}
};

template<class T>
sharedptr<T> CSingleFactory<T>::g_singlePtr;

template<class T>
CMutex CSingleFactory<T>::g_mutexPtr;

DEF_END_UTUTIL

#endif // __SINGLEFACTORY_H__
