﻿#if defined(WIN32)
#	ifndef WIN32_LEAN_AND_MEAN
#		define WIN32_LEAN_AND_MEAN 
#	endif
#	include <shlwapi.h>
#	include <WinSock2.h>
#endif

#include <errno.h>
#include <algorithm>
#include <cmath>
#include <utility>
#include <ctype.h>
#include <map>
#include <stdlib.h>
#include "ututil_shareapi.h"
#include "ututil_algorithm.h"
#include "ututil_time.h"
//#include "StringTools.h"

#ifdef LINUX
#	include <iconv.h>
#	include <pthread.h>
#	include <sys/eventfd.h>
#	include <execinfo.h>
#	include <signal.h>
#	include <sys/wait.h>
#	include <dlfcn.h>
#elif defined(WIN32)
#	pragma warning( disable : 4091 )
#	pragma comment(lib, "DbgHelp.lib")
#	pragma comment(lib, "Shlwapi.lib")
#	include <process.h>
#	include <intrin.h>
#	include <DbgHelp.h>
#endif

#include "__ututil_tools.h"

//#pragma warning(disable:4307)
//#pragma warning(disable:4996)

#define CPU_TICK_TIME 16

DEF_BEG_UTUTIL

using std::min;

DWORD ShareApi::GetLastError()
{
#ifdef WIN32
	return ::GetLastError();
#elif defined(LINUX)
	return errno;
#endif
}

inline int GetPosValue(int curValue)
{
#ifdef WIN32
	switch (curValue)
	{
	case FILE_SEEK_SET:
		return FILE_BEGIN;
	case FILE_SEEK_CUR:
		return FILE_CURRENT;
	case FILE_SEEK_END:
		return FILE_END;
	}
#elif defined(LINUX)
	switch (curValue)
	{
	case FILE_SEEK_SET:
		return SEEK_SET;
	case FILE_SEEK_CUR:
		return SEEK_CUR;
	case FILE_SEEK_END:
		return SEEK_END;
	}
#endif
	return 0;
}

FILE_HANDLE ShareApi::OpenFile(const TCHAR* szFileName, unsigned int mode)
{
#ifdef WIN32
	DWORD dwAccess = 0,dwShare = 0,dwCreate = 0,dwFlagsAndAttributes = 0;
	// 访问模式
	if(mode & 1) dwAccess =  GENERIC_READ;	// 只读
	if((mode>>1) & 1) dwAccess |= GENERIC_WRITE;// 只写
	if((mode>>2) & 1) dwAccess = GENERIC_READ|GENERIC_WRITE;// 读写
	// 共享模式
	if((mode>>3) & 1) dwShare = FILE_SHARE_READ | FILE_SHARE_WRITE | FILE_SHARE_DELETE; // 其它进程也可以打开文件
	else dwShare = 0; // 其它进程不可以打开文件
	// 是否清空文件
	if((mode>>5) & 1) dwCreate = CREATE_ALWAYS;
	else dwCreate = OPEN_EXISTING;	// 打开已存在的文件
	// 是否为隐藏文件
	if((mode>>6) & 1) dwFlagsAndAttributes = FILE_ATTRIBUTE_HIDDEN;
	else dwFlagsAndAttributes = FILE_ATTRIBUTE_NORMAL;
	// 异步操作I/O
	if((mode>>7) & 1) dwFlagsAndAttributes |= FILE_FLAG_OVERLAPPED;
	if ((mode >> 8) & 1)
	{
		// 文件不存在则创建文件，存在则打开
		dwCreate = OPEN_ALWAYS;
		// 是否创建新文件,如果存在文件则函数返回失败
		if ((mode >> 9) & 1) dwCreate = CREATE_NEW;
	}


	FILE_HANDLE hFile = (FILE_HANDLE)::CreateFile(szFileName,dwAccess,dwShare,NULL,dwCreate,dwFlagsAndAttributes,NULL);
	if((UTUTIL_HANDLE)hFile == INVALID_HANDLE_VALUE)
	{
		return NULL;
	}
#elif defined(LINUX)
	mode_t permission = S_IRWXG|S_IRWXU|S_IRWXO;
	int dwAccess = 0;
	if(mode & 1){
		 dwAccess = O_RDONLY;	// 只读
		 permission = 0;
	}
		 
	if((mode>>1) & 1) dwAccess |= O_WRONLY; // 只写
	if((mode>>2) & 1) dwAccess |= O_RDWR; // 读写
	// 共享模式,隐藏文件功能移除
	if((mode>>5) & 1) dwAccess |= O_TRUNC; // 清空内容
	if((mode >> 7) & 1) dwAccess |= O_NONBLOCK; // 异步I/O
	if((mode>>8) & 1)
	{
		dwAccess |= O_CREAT; // 不存在文件则创建
		if((mode>>9) & 1) dwAccess |= O_EXCL; // 存在文件则失败
	}
	int file = open64(szFileName, dwAccess | O_CLOEXEC, permission);
	if (file == -1)
	{
		return 0;
	}
	FILE_HANDLE hFile = file;
#endif

	return hFile;
}

INT64 ShareApi::GetFileSizeEx(FILE_HANDLE hFile)
{
#ifdef WIN32
	LARGE_INTEGER nFileSize;
	if (!::GetFileSizeEx((UTUTIL_HANDLE)hFile, &nFileSize)) return -1;
	return nFileSize.QuadPart;
#elif defined(LINUX)
	struct stat64 buf;
	if (fstat64(hFile, &buf) != 0) return -1;
	return buf.st_size;
#endif
}

int ShareApi::GetFileSize(FILE_HANDLE hFile)
{
#ifdef WIN32
	DWORD nFileSize = ::GetFileSize((UTUTIL_HANDLE)hFile, NULL);
	if (nFileSize == INVALID_FILE_SIZE){
		return -1;
	}
	return nFileSize;
#elif defined(LINUX)
	struct stat buf;
	if (fstat(hFile, &buf) != 0) return -1;
	return buf.st_size;
#endif
}

BOOL ShareApi::SetFilePointerEx(FILE_HANDLE hFile, INT64 dwOffset, int iWhence)
{
	int iw = GetPosValue(iWhence);
#ifdef WIN32
	LARGE_INTEGER nPointPos;
	nPointPos.QuadPart = dwOffset;
	if (!::SetFilePointerEx((UTUTIL_HANDLE)hFile, nPointPos, NULL, iw)) return FALSE;
#elif defined(LINUX)
	INT64 nPos = (INT64)lseek64(hFile, (off64_t)dwOffset, iw);
	if(nPos == -1) return FALSE;
#endif
	return TRUE;
}

BOOL ShareApi::SetFilePointer(FILE_HANDLE hFile, int dwOffset, int iWhence)
{
	int iw = GetPosValue(iWhence);
#ifdef WIN32
	if (::SetFilePointer((UTUTIL_HANDLE)hFile, dwOffset, NULL, iw) == INVALID_SET_FILE_POINTER){
		return FALSE;
	}
#elif defined(LINUX)
	INT64 nPos = (INT64)lseek(hFile, dwOffset, iw);
	if(nPos == -1) return FALSE;
#endif
	return TRUE;
}

INT64 ShareApi::GetFilePointerEx(FILE_HANDLE hFile)
{
#ifdef WIN32
	LARGE_INTEGER nPointPos = {0}, curPointPos;
	if (!::SetFilePointerEx((UTUTIL_HANDLE)hFile, nPointPos, &curPointPos, FILE_CURRENT)) return -1;
	return curPointPos.QuadPart;
#elif defined(LINUX)
	INT64 nPos = (INT64)lseek64(hFile, 0, SEEK_CUR);
	return nPos;
#endif
}

int ShareApi::GetFilePointer(FILE_HANDLE hFile)
{
#ifdef WIN32
	DWORD dwPtr = ::SetFilePointer((UTUTIL_HANDLE)hFile, 0, NULL, FILE_CURRENT);
	if (dwPtr == INVALID_SET_FILE_POINTER){
		return -1;
	}
	return dwPtr;
#elif defined(LINUX)
	return lseek(hFile, 0, SEEK_CUR);
#endif
}

void ShareApi::CloseFile(FILE_HANDLE hFile)
{
#ifdef WIN32
	CloseHandle((UTUTIL_HANDLE)hFile);
#elif defined(LINUX)
	close(hFile);
#endif
}

fsize_t ShareApi::ReadFile(FILE_HANDLE hFile, void *buf, fsize_t size)
{
#ifdef WIN32
	DWORD nRead = 0;
	if (!::ReadFile((UTUTIL_HANDLE)hFile, buf, (DWORD)size, &nRead, NULL))
	{
		nRead = -1;
	}
#elif defined(LINUX)
	fsize_t nRead = 0;
	nRead = read(hFile, buf, size);
#endif
	return nRead;
}

fsize_t ShareApi::WriteFile(FILE_HANDLE hFile, const void* buf, fsize_t size)
{
#ifdef WIN32
	DWORD nWrite = 0;
	if (!::WriteFile((UTUTIL_HANDLE)hFile, buf, (DWORD)size, &nWrite, NULL))
	{
		nWrite = -1;
	}
#elif defined(LINUX)
	fsize_t nWrite = 0;
	nWrite = write(hFile, buf, size);
#endif
	return nWrite;
}

#ifdef WIN32
// static inline BOOL In_IsDirent(const TCHAR* szDir, PWIN32_FIND_DATA pData)
// {
// 	UTUTIL_HANDLE hFile = ::FindFirstFile(szDir, pData);
// 	if (hFile != INVALID_HANDLE_VALUE)
// 	{
// 		if (pData->dwFileAttributes == FILE_ATTRIBUTE_DIRECTORY)
// 		{
// 			::FindClose(hFile);
// 			return TRUE;
// 		}
// 		::FindClose(hFile);
// 	}
// 	return FALSE;
// }

static inline void FileTimeToTime_t(const FILETIME& ft, time_t& t)
{
	LONGLONG  ltime_t;
	ltime_t = ((INT64)ft.dwHighDateTime << 32) + ft.dwLowDateTime;
	t = ((LONGLONG)(ltime_t - 116444736000000000) / 10000000);
}
#endif

FILE_HANDLE ShareApi::FindFirstFile(const TCHAR *szDir, PFILEFINDINFO pInfo)
{
	FILE_HANDLE hFind = (FILE_HANDLE)NULL;
#ifdef WIN32
	WIN32_FIND_DATA info;
	TCHAR szFindPath[MAX_PATH] = { 0 };
	size_t dirLen = _tcslen(szDir);
	tcscpy_safe(szFindPath, sizeof(szFindPath), szDir);
	if (szFindPath[dirLen - 1] == '/' || szFindPath[dirLen - 1] == '\\') szFindPath[--dirLen] = 0;
	std::replace(szFindPath, szFindPath + dirLen, '/', '\\');
	// 检查路径是否为目录
	if (!IsDirent(szFindPath)) {
		return FILE_HANDLE((UTUTIL_HANDLE*)NULL);
	}
	// windows 需要加*.*遍历
	tcscpy_safe(szFindPath + _tcslen(szFindPath), sizeof(szFindPath) - _tcslen(szFindPath), TEXT("\\*.*"));
	hFind = (FILE_HANDLE)::FindFirstFile(szFindPath, &info);
	if ((UTUTIL_HANDLE)hFind == INVALID_HANDLE_VALUE) {
		return FILE_HANDLE((UTUTIL_HANDLE*)NULL);
	}
	tcscpy_safe(pInfo->szFileName, sizeof(pInfo->szFileName), info.cFileName);
	if (info.dwFileAttributes == FILE_ATTRIBUTE_DIRECTORY) {
		pInfo->fwFileAttributes = FILE_DIR;
	}
	else if (info.dwFileAttributes == FILE_ATTRIBUTE_ARCHIVE){
		pInfo->fwFileAttributes = FILE_NORMAL;
	}
	else {
		pInfo->fwFileAttributes = FILE_SYSTYPE;
	}
	FileTimeToTime_t(info.ftCreationTime, pInfo->tCreateTime);
	FileTimeToTime_t(info.ftLastWriteTime, pInfo->tUpdateTime);
	pInfo->dwFilesize = ((INT64)info.nFileSizeHigh * ((INT64)MAXDWORD + 1)) + (INT64)info.nFileSizeLow;
#elif defined(LINUX)
	DirInfo *pDir = (DirInfo*)malloc(sizeof(DirInfo));
	if (!pDir) return 0;
	memset(pDir, 0, sizeof(DirInfo));

	pDir->pDir = opendir(szDir);
	if (pDir->pDir != NULL)
	{
		struct dirent *dirInfo;
		dirInfo = readdir(pDir->pDir);
		if (dirInfo != NullPtr)
		{
			// 获取文件状态信息
			TCHAR szFilePath[MAX_PATH];
			tcscpy_safe(szFilePath, sizeof(szFilePath), szDir);
			PathAppend(szFilePath, dirInfo->d_name);
			struct stat64 info;
			memset(&info, 0, sizeof(info));
			stat64(szFilePath, &info);
			pInfo->dwFilesize = info.st_size;
			pInfo->tCreateTime = info.st_ctime;
			pInfo->tUpdateTime = info.st_mtime;
			if (S_ISDIR(info.st_mode))
			{
				pInfo->fwFileAttributes = FILE_DIR;
				pInfo->dwFilesize = 0;
			}
			else if (S_ISREG(info.st_mode)) {
				pInfo->fwFileAttributes = FILE_NORMAL;
			}
			else {
				pInfo->fwFileAttributes = FILE_SYSTYPE;
			}

			tcscpy_safe(pInfo->szFileName, sizeof(pInfo->szFileName), dirInfo->d_name);
			tcscpy_safe(pDir->szDir, sizeof(pInfo->szFileName), szDir);
			hFind = (FILE_HANDLE)pDir;
		}
		else 
		{
			int ret = errno;
			closedir(pDir->pDir);
			free(pDir);
			errno = ret;
		}
	}
#endif

	return hFind;
}

BOOL ShareApi::FindNextFile(FILE_HANDLE hFile, PFILEFINDINFO pInfo)
{
	BOOL bRet = TRUE;
#ifdef WIN32

	WIN32_FIND_DATA info;
	bRet = ::FindNextFile((UTUTIL_HANDLE)hFile, &info);
	if (bRet)
	{
		tcscpy_safe(pInfo->szFileName, sizeof(pInfo->szFileName), info.cFileName);
		if (info.dwFileAttributes == FILE_ATTRIBUTE_DIRECTORY){
			pInfo->fwFileAttributes = FILE_DIR;
		}
		else if(info.dwFileAttributes == FILE_ATTRIBUTE_ARCHIVE){
			pInfo->fwFileAttributes = FILE_NORMAL;
		}
		else {
			pInfo->fwFileAttributes = FILE_SYSTYPE;
		}
		FileTimeToTime_t(info.ftCreationTime, pInfo->tCreateTime);
		FileTimeToTime_t(info.ftLastWriteTime, pInfo->tUpdateTime);
		pInfo->dwFilesize = ((INT64)info.nFileSizeHigh * ((INT64)MAXDWORD + 1))+ (INT64)info.nFileSizeLow;
	}
#elif defined(LINUX)
	struct dirent *dir = readdir(((DirInfo*)hFile)->pDir);
	if (dir != NullPtr)
	{
		// 文件状态信息
		TCHAR szFilePath[MAX_PATH];
		tcscpy_safe(szFilePath, sizeof(szFilePath), ((DirInfo*)hFile)->szDir);
		PathAppend(szFilePath, dir->d_name);
		struct stat64 info;
		stat64(szFilePath, &info);
		pInfo->dwFilesize = info.st_size;
		pInfo->tCreateTime = info.st_ctime;
		pInfo->tUpdateTime = info.st_mtime;
		if (S_ISDIR(info.st_mode))
		{
			pInfo->fwFileAttributes = FILE_DIR;
			pInfo->dwFilesize = 0;
		}
		else if (S_ISREG(info.st_mode)) {
			pInfo->fwFileAttributes = FILE_NORMAL;
		}
		else {
			pInfo->fwFileAttributes = FILE_SYSTYPE;
		}
		tcscpy_safe(pInfo->szFileName, sizeof(pInfo->szFileName), dir->d_name);
	}
	else {
		bRet = FALSE;
	}
#endif

	return bRet;
}

void ShareApi::FindClose(FILE_HANDLE hFile)
{
	if (hFile)
	{
#ifdef WIN32
		::FindClose((UTUTIL_HANDLE)hFile);
#elif defined(LINUX)
		closedir(((DirInfo*)hFile)->pDir);
		int err = errno;
		free(((DirInfo*)hFile));
		errno = err;
#endif
	}
}

void ShareApi::PathAppend(TCHAR *szSrcDir, const TCHAR *szSubDir)
{
	size_t srclen = _tcslen(szSrcDir);
	size_t sublen = _tcslen(szSubDir);
	if (srclen + sublen + 1 < MAX_PATH)
	{
		if (szSrcDir[srclen - 1] != '/' && szSrcDir[srclen-1] != '\\')
		{
			szSrcDir[srclen++] = '/';
		}
		tcscpy_safe(szSrcDir + srclen, MAX_PATH - srclen, szSubDir);
	}
}

BOOL ShareApi::IsDirent(const TCHAR* szDir)
{
#ifdef WIN32
	return (PathIsDirectory(szDir) == FILE_ATTRIBUTE_DIRECTORY);
#elif defined(LINUX)
	struct stat info;
	if(stat(szDir, &info) != -1){
		if(S_ISDIR(info.st_mode)) return TRUE;
	}
#endif

	return FALSE;
}

BOOL ShareApi::FilePathExists(const TCHAR* szFileName)
{
#ifdef LINUX
	if(_taccess(szFileName,F_OK) == 0) return TRUE;
#elif defined(WIN32)
	if(_taccess(szFileName, 0) != -1) return TRUE;
#endif

	return FALSE;
}

BOOL ShareApi::GetCurrentDirectory(DWORD size, TCHAR *szPath)
{
#ifdef WIN32
	if (::GetCurrentDirectory(size, szPath))
		return TRUE;
#elif defined(LINUX)
	if (::getcwd(szPath, size))
		return TRUE;
#endif
	return FALSE;
}

BOOL ShareApi::CreateDirectory(const TCHAR* szPathName)
{
#ifdef WIN32
	return ::CreateDirectory(szPathName, NULL);
#elif defined(LINUX)
	if (!::mkdir(szPathName, S_IRWXU | S_IRWXG | S_IROTH | S_IXOTH))
		return TRUE;
#endif
	return FALSE;
}

TCHAR* ShareApi::Global_GetLastError(DWORD* pError /* = NULL */)
{
	TCHAR* sInfo = NULL; 

	DWORD iErr = 0;
	if (!pError){
		iErr = ShareApi::GetLastError();
	}else{
		iErr = *pError;
	}

#ifdef WIN32
	void *hocal = NULL;
	DWORD hOk = FormatMessage(FORMAT_MESSAGE_ALLOCATE_BUFFER|FORMAT_MESSAGE_FROM_SYSTEM|
		FORMAT_MESSAGE_IGNORE_INSERTS,NULL,iErr, MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT)
		,(LPTSTR)&hocal,MAX_PATH*3,NULL);
	if (hOk)
	{
		if (hocal == NULL) { sInfo = NULL; return sInfo; }
		size_t iSize = _tcslen((const TCHAR*)hocal);
		sInfo = new TCHAR[iSize + 1];
		tcscpy_safe(sInfo, iSize + 1, (const TCHAR*)hocal);
		LocalFree(hocal);
	}
#elif defined(LINUX)
	const TCHAR *sErr = strerror(iErr);
	int iSize = _tcslen(sErr);
	sInfo = new TCHAR[iSize + 1];
	tcscpy_safe(sInfo, iSize + 1, (const TCHAR*)sErr);
#endif

	return sInfo;
}

void ShareApi::Global_FreeMem(TCHAR* sInfo)
{
	if (sInfo)
	{
		delete[]sInfo;
		sInfo = NULL;
	}
}

BOOL ShareApi::AS_CPY_UTF8(size_t size, char *des, const char *src)
{
	if (GbkToUtf8(des, size, src) == (size_t)-1) return FALSE;

	return TRUE;
}

size_t ShareApi::GbkToUtf8(char* des, size_t size, const char* src, size_t srcLen /* = 0 */)
{
	size_t retSize = -1;
	if (size < 1) return 0;
	if (srcLen == 0) srcLen = strlen(src);
#ifdef WIN32
	WCHAR *wsDes = new WCHAR[srcLen + 1];
	retSize = (size_t)MultiByteToWideChar(CP_ACP, 0, src, (int)srcLen, wsDes, (int)srcLen + 1);
	if (retSize != -1)
	{
		retSize = (size_t)WideCharToMultiByte(CP_UTF8, 0, wsDes, (int)retSize, des, (int)size, NULL, NULL);
		des[qmin(retSize, size - 1)] = '\0';
	}
	delete[]wsDes;
	wsDes = NULL;
#elif defined(LINUX)
	iconv_t handle = iconv_open("UTF-8", "GBK");
	if (-1 != (long)handle)
	{
		char ** instr = (char**)&src;
		char * outstr = des;
		size_t outSize = size;
		retSize = (size_t)iconv(handle, instr, &srcLen, &outstr, &outSize);
		if (retSize != (size_t)-1)
		{
			retSize = size - outSize;
			des[qmin(retSize, size - 1)] = 0;
		}
		iconv_close(handle);
	}
#endif
	return retSize;
}

BOOL ShareApi::UTF8_CPY_AS(size_t size, char *des, const char* src)
{
	if (Utf8ToGbk(des, size, src) == (size_t)-1) return FALSE;

	return TRUE;
}

size_t ShareApi::Utf8ToGbk(char* des, size_t size, const char* src, size_t srcLen /* = 0 */)
{
	size_t retSize = -1;
	if (size < 1) return 0;
	if (srcLen == 0) srcLen = strlen(src);
#ifdef WIN32
	WCHAR *wsDst = new WCHAR[srcLen + 1];
	retSize = (size_t)MultiByteToWideChar(CP_UTF8, 0, src, (int)srcLen, wsDst, (int)srcLen);
	if (retSize != -1)
	{
		retSize = (size_t)WideCharToMultiByte(CP_ACP, 0, wsDst, (int)retSize, des, (int)size, NULL, NULL);
		des[qmin(retSize, size - 1)] = '\0';
	}
	delete[]wsDst;
	wsDst = NULL;
#elif defined(LINUX)
	iconv_t handle = iconv_open("GBK", "UTF-8");
	if (-1 != (long)handle)
	{
		char ** instr = (char**)&src;
		char * outstr = des;
		size_t outSize = size;
		retSize = (size_t)iconv(handle, instr, &srcLen, &outstr, &outSize);
		if (retSize != (size_t)-1)
		{
			retSize = size - outSize;
			des[qmin(retSize, size - 1)] = 0;
		}
		iconv_close(handle);
	}
#endif
	return retSize;
}

BOOL ShareApi::AS_CPY_WS(size_t size, WCHAR *des, const char* src)
{
	if (GbkToUnicode(des, size, src) == (size_t)-1) return FALSE;

	return TRUE;
}

size_t ShareApi::GbkToUnicode(WCHAR* des, size_t size, const char* src, size_t srcLen)
{
	size_t retSize = -1;
	if (size < 1) return 0;
	if (srcLen == 0) srcLen = strlen(src);
#ifdef WIN32
	retSize = (size_t)::MultiByteToWideChar(CP_ACP, 0, src, (int)srcLen, des, (int)size);
	des[qmin(retSize, size - 1)] = '\0';
#elif defined(LINUX)
	iconv_t handle = iconv_open("UCS-2LE", "GBK");
	if (-1 != (long)handle)
	{
		size *= sizeof(WCHAR);
		char ** instr = (char**)&src;
		if(sizeof(WCHAR) == 2)
		{
			char * outstr = (char*)des;
			size_t outSize = size;
			retSize = (size_t)iconv(handle, instr, &srcLen, &outstr, &outSize);
			if (retSize != (size_t)-1)
			{
				retSize = size - outSize;
				retSize /= sizeof(WCHAR);
			}
		}
		else
		{
			char *temp = new char[size];
			size_t outSize = size;
			char *outptr = temp;
			retSize = (size_t)iconv(handle, instr, &srcLen, &outptr, &outSize);
			if (retSize != (size_t)-1)
			{
				retSize = size - outSize;
				size_t end = copy_seq(des, size / sizeof(WCHAR) - 1, (short*)temp, retSize / 2);
				des[end] = 0;
				retSize = end;
			}
			delete[]temp;
		}
		iconv_close(handle);
	}
#endif
	return retSize;
}

BOOL ShareApi::UTF8_CPY_WS(size_t size, WCHAR *des, const char *src)
{
	if (Utf8ToUnicode(des, size, src) == (size_t)-1) return FALSE;

	return TRUE;
}

size_t ShareApi::Utf8ToUnicode(WCHAR* des, size_t size, const char* src, size_t srcLen /* = 0 */)
{
	size_t retSize = -1;
	if (size < 1) return 0;
	if (srcLen == 0) srcLen = strlen(src);
#ifdef WIN32
	retSize = (size_t)::MultiByteToWideChar(CP_UTF8, 0, src, (int)srcLen, des, (int)size);
	des[qmin(retSize, size - 1)] = '\0';
#elif defined(LINUX)
	iconv_t handle = iconv_open("UCS-2LE", "UTF-8");
	if (-1 != (long)handle)
	{
		char ** instr = (char**)&src;
		size *= sizeof(WCHAR);
		if (sizeof(WCHAR) == 2)
		{
			char * outstr = (char*)des;
			size_t outSize = size;
			retSize = (size_t)iconv(handle, instr, &srcLen, &outstr, &outSize);
			if (retSize != (size_t)-1)
			{
				retSize = size - outSize;
				retSize /= sizeof(WCHAR);
			}
		}
		else
		{
			char *temp = new char[size];
			size_t outSize = size;
			char *outptr = temp;
			retSize = (size_t)iconv(handle, instr, &srcLen, &outptr, &outSize);
			if (retSize != (size_t)-1)
			{
				retSize = size - outSize;
				size_t end = copy_seq(des, size / sizeof(WCHAR) - 1, (short*)temp, retSize / 2);
				des[end] = 0;
				retSize = end;
			}
			delete[]temp;
		}
		iconv_close(handle);
	}
#endif
	return retSize;
}

BOOL ShareApi::WS_CPY_AS(size_t size, char *des, const WCHAR *src)
{
	if (UnicodeToGbk(des, size, src) == (size_t)-1) return FALSE;

	return TRUE;
}

size_t ShareApi::UnicodeToGbk(char* des, size_t size, const WCHAR* src, size_t srcLen /* = 0 */)
{
	size_t retSize = -1;
	if (size < 1) return 0;
	if (srcLen == 0) srcLen = wcslen(src);
#ifdef WIN32
	retSize = (size_t)::WideCharToMultiByte(CP_ACP, 0, src, (int)srcLen, des, (int)size, NULL, NULL);
	des[qmin(retSize, size - 1)] = '\0';
#elif defined(LINUX)
	iconv_t handle = iconv_open("GBK", "UCS-2LE");
	if (-1 != (long)handle)
	{
		size_t inputLen = srcLen * 2;
		short *tempSrc = new short[srcLen];
		copy_seq(tempSrc, srcLen, src, srcLen);
		char *instr = (char*)tempSrc;
		char *outstr = des;
		size_t outSize = size;
		retSize = (size_t)iconv(handle, &instr, &inputLen, &outstr, &outSize);
		if (retSize != (size_t)-1)
		{
			retSize = size - outSize;
			des[qmin(retSize, size - 1)] = 0;
		}
		delete[]tempSrc;
		iconv_close(handle);
	}
#endif
	return retSize;
}

BOOL ShareApi::WS_CPY_UTF8(size_t size, char *des, const WCHAR *src)
{
	if (UnicodeToUtf8(des, size, src) == (size_t)-1) return FALSE;

	return TRUE;
}

size_t ShareApi::UnicodeToUtf8(char* des, size_t size, const WCHAR* src, size_t srcLen /* = 0 */)
{
	size_t retSize = -1;
	if (srcLen == 0) srcLen = wcslen(src);
#ifdef WIN32
	retSize = (size_t)::WideCharToMultiByte(CP_UTF8, 0, src, (int)srcLen, des, (int)size, NULL, NULL);
	des[qmin(retSize, size - 1)] = L'\0';
#elif defined(LINUX)
	iconv_t handle = iconv_open("UTF-8", "UCS-2LE");
	if (-1 != (long)handle)
	{
		size_t inputLen = srcLen * 2;
		short *tempSrc = new short[srcLen];
		copy_seq(tempSrc, srcLen, src, srcLen);
		char *instr = (char*)tempSrc;
		char *outstr = des;
		size_t outSize = size;
		retSize = (size_t)iconv(handle, &instr, &inputLen, &outstr, &outSize);
		if (retSize != (size_t)-1)
		{
			retSize = size - outSize;
			des[qmin(retSize, size - 1)] = 0;
		}
		delete []tempSrc;
		iconv_close(handle);
	}
#endif
	return retSize;
}

size_t ShareApi::lltoa(INT64 value, char *szValue, size_t size)
{
#ifdef WIN32
	return sprintf_s(szValue, size, "%I64d", value);
#elif defined(LINUX)
	return _snprintf(szValue, size, "%lld", value);
#endif
}

size_t ShareApi::ultoa(UINT64 value, char *szValue, size_t size)
{
#ifdef WIN32
	return sprintf_s(szValue, size, "%I64u", value);
#elif defined(LINUX)
	return _snprintf(szValue, size, "%llu", value);
#endif
}

size_t ShareApi::ltoa(long_t value, char *szValue, size_t size)
{
#ifdef WIN32
#	ifdef _WIN64
		return sprintf_s(szValue, size, "%I64d", value);
#	else 
		return sprintf_s(szValue, size, "%d", value);
#	endif
#elif defined(LINUX)
	return sprintf_safe(szValue, size, "%ld", value);
#endif
}

size_t ShareApi::itoa(int value, char *szValue, size_t size)
{
#ifdef WIN32
	return sprintf_s(szValue, size, "%d", value);
#elif defined(LINUX)
	return sprintf_safe(szValue, size, "%d", value);
#endif
}

inline size_t _trim_float_zero_(char* szValue, size_t size)
{
	// 去掉后面的0,但至少保留一位小数点
	while (size > 0)
	{
		if (szValue[size - 1] == '0' && szValue[size - 2] != '.'){
			szValue[--size] = '\0';
		}else{
			break;
		}
	}
	return size;
}

size_t ShareApi::ftoa(float value, char *szValue, size_t size, size_t dotcount)
{
	/*if (size <= 0) return 0;

	int n = (int)std::abs(value), tmp;
	int iPos = 0, iStartPos = 0;
	if (value < MINUX_FLT_DOT){
		szValue[iPos++] = '-';
		++iStartPos;
	}

	value = std::abs(value);
	value -= (float)n;

	while (n > 0)
	{
		tmp = n % 10;
		n /= 10;
		if (iPos < size){
			szValue[iPos++] = tmp + '0';
		}else{
			break;
		}
	}

	if (iPos == 0 || (iPos == 1 && iStartPos == 1))
	{
		if (iPos < size){
			szValue[iPos++] = '0';
		}
	}

	std::reverse(szValue + iStartPos, szValue + iPos);
	if (iPos < size){
		szValue[iPos++] = '.';
	}

	dotcount = min(dotcount, FLT_DOT_COUNT);
	while (dotcount > 0)
	{
		value *= 10.f;
		--dotcount;
	}

	n = (int)value;
	n = (value - (float)n) * 10 > 5 ? n + 1: n;


	iStartPos = iPos;
	while (n > 0)
	{
		tmp = n % 10;
		n /= 10;
		if (iPos < size){
			szValue[iPos++] = tmp + '0';
		}else{
			break;
		}
	}
	// 至少保留一位0
	if (iStartPos == iPos && iPos < size){
		szValue[iPos++] = '0';
	}
	std::reverse(szValue + iStartPos, szValue + iPos);

	if (size > 0)
	{
		iPos = (iPos < size) ? iPos : size - 1;
		szValue[iPos] = 0;
	}

	_trim_float_zero_(szValue, iPos);

	return iPos;*/
	dotcount = qmin(dotcount, (size_t)FLT_DOT_COUNT);
	return lftoa(value, szValue, size, dotcount);
}

size_t ShareApi::lftoa(double value, char *szValue, size_t size, size_t dotcount)
{
	return llftoa((long double)value, szValue, size, dotcount);
}

size_t ShareApi::llftoa(long double value, char *szValue, size_t size, size_t dotcount)
{
	if (size <= 0) return 0;

	long long n = (long long)std::fabs(value), tmp;
	size_t iPos = 0, iStartPos = 0;
	if (value <= MINUX_DOE_DOT)
	{
		szValue[iPos++] = '-';
		++iStartPos;
	}
	value = std::fabs(value);
	value -= (long double)n;

	while (n > 0)
	{
		tmp = n % 10;
		n /= 10;
		if (iPos < size){
			szValue[iPos++] = (char)(tmp + '0');
		}else{
			break;
		}
	}

	if (iPos == 0 || (iPos == 1 && iStartPos == 1))
	{
		if (iPos < size){
			szValue[iPos++] = '0';
		}
	}

	std::reverse(szValue + iStartPos, szValue + iPos);
	if (iPos < size){
		szValue[iPos++] = '.';
	}

	dotcount = qmin(dotcount, (size_t)DOE_DOT_COUNT);
	while (dotcount > 0)
	{
		value *= 10.f;
		--dotcount;
		if (value < 1.f && iPos < size){
			szValue[iPos++] = '0';
		}
	}

	n = (long long)value;
	n = (value - (long double)n) * 10 > 5 ? n + 1 : n;

	iStartPos = iPos;
	while (n > 0)
	{
		tmp = n % 10;
		n /= 10;
		if (iPos < size){
			szValue[iPos++] = (char)(tmp + '0');
		}else{
			break;
		}
	}
	// 至少保留一位0
	if (iStartPos == iPos && iPos < size){
		szValue[iPos++] = '0';
	}
	std::reverse(szValue + iStartPos, szValue + iPos);

	if (size > 0)
	{
		iPos = (iPos < size) ? iPos : size - 1;
		szValue[iPos] = 0;
	}

	_trim_float_zero_(szValue, iPos);

	return (int)iPos;
}

size_t ShareApi::strnicmp(const char* str1, const char *str2, size_t count)
{
	if (str1 == NULL && str2 == NULL)
	{
		return 0;
	}
	else if (str1 == NULL && str2 != NULL)
	{
		return -1;
	}
	else if (str1 != NULL && str2 == NULL)
	{
		return 1;
	}

	while (count > 0)
	{
		if (*str1 == '\0' || *str2 == '\0')
		{
			break;
		}

		if (toupper(*str1) != toupper(*str2))
		{
			break;
		}
		++str1;
		++str2;
		--count;
	}

	int nRet = 0;

	if (count == 0)
	{
		nRet = 0;
	}
	else
	{
		if (*str1 != '\0' && *str2 != '\0')
		{
			if (toupper(*str1) > toupper(*str2))
			{
				nRet = 1;
			}
			else if (toupper(*str1) < toupper(*str2))
			{
				nRet = -1;
			}
		}
		else if (*str1 == '\0' && *str2 != '\0')
		{
			nRet = -1;
		}
		else if (*str1 != '\0' && *str2 == '\0')
		{
			nRet = 1;
		}
	}

	return nRet;
}

/********************** 线程相关(start) ***********************/
typedef struct __INLINE_THREAD_DATA
{
	void *arg;	// 线程函数参数
	ThreadRoutine pThreadFunc;	// 线程函数指针
	size_t bSuspend;
	__INLINE_THREAD_DATA(void* value, ThreadRoutine func, size_t suspend)
	{
		arg = value;
		pThreadFunc = func;
		bSuspend = suspend;
	}
}_INLINE_THREAD_DATA, *_PINLINE_THREAD_DATA;

#ifdef WIN32
static unsigned _stdcall _windows_start_address(void* arg)
{
	_PINLINE_THREAD_DATA pData = (_PINLINE_THREAD_DATA)arg;
	DWORD dwThreadRet = pData->pThreadFunc(pData->arg);
	delete pData;
	pData = NULL;
	::_endthreadex(dwThreadRet);
	return 0;
}
#elif defined(LINUX)
static void* _stdcall _linux_start_routine(void* arg)
{
	_PINLINE_THREAD_DATA pData = (_PINLINE_THREAD_DATA)arg;
	pData->pThreadFunc(pData->arg);
	delete pData;
	pData = NULL;
	return NULL;
}
#endif

// 创建线程
THREADPTR ShareApi::BeginThread(ThreadRoutine pFunc, void* arg, UINT stack_size /* = 0 */, ULONG* pThreadId /* = NULL */)
{
	_INLINE_THREAD_DATA *pInfo = new _INLINE_THREAD_DATA(arg, pFunc, 0);

	if (stack_size <= 0){
		stack_size = THREAD_STACK_MIN;
	}
#ifdef WIN32
	THREADPTR thHandle = 0;
	// 创建常规线程
	thHandle = _beginthreadex(NULL, stack_size, &_windows_start_address, pInfo, 0, (UINT*)pThreadId);
	if (thHandle <= 0) return 0;

#elif defined(LINUX)
	THREADPTR thHandle = 0;
	pthread_attr_t attr;
	int iRet = pthread_attr_init(&attr);
	if (iRet == 0)
	{
		if (pthread_attr_setstacksize(&attr, stack_size) == 0)
		{
			if (pthread_create(&thHandle, &attr, &_linux_start_routine, (void*)pInfo) != 0)
			{
				iRet = 99;
			}
		}// if 设置化栈大小
		pthread_attr_destroy(&attr);
	}// if 初始化属性

	if (iRet > 0) return 0;
	else if (pThreadId) *pThreadId = thHandle;
#endif
	return thHandle;
}

ULONG ShareApi::GetCurrentThreadId()
{
#ifdef WIN32
	return ::GetCurrentThreadId();
#elif defined(LINUX)
	return (ULONG)::pthread_self();
#endif
}

ULONG ShareApi::GetCurrentProcessId()
{
#ifdef WIN32
	return ::GetCurrentProcessId();
#elif defined(LINUX)
	return (ULONG)::getpid();
#endif
}

void ShareApi::CloseThread(THREADPTR thHandle)
{
#ifdef WIN32
	::CloseHandle((UTUTIL_HANDLE)thHandle);
#elif defined(LINUX)
	::pthread_detach(thHandle);
#endif
}

BOOL ShareApi::Thread_Join(THREADPTR thHandle)
{
	if (!thHandle) return FALSE;
#ifdef WIN32
	// windows使用等待线程句柄有信号方式
	if (::WaitForSingleObject((UTUTIL_HANDLE)thHandle, INFINITE) != WAIT_OBJECT_0)
	{
		return FALSE;
	}
#elif defined(LINUX)
	if (pthread_join(thHandle, NULL) != 0)
	{
		return FALSE;
	}
#endif
	return TRUE;
}
/********************** 线程相关(end) ***********************/

/********************** 同步相关(start) ***********************/
SYNCHANDLE ShareApi::CreateMutex()
{
	PMTINFO hMutex = (PMTINFO)malloc(sizeof(MTINFO));
	if (!hMutex) return 0;
	memset(hMutex, 0, sizeof(*hMutex));
#ifdef WIN32
	::InitializeCriticalSection(&(hMutex->mutex));
#elif defined(LINUX)
	if (pthread_mutex_init(&(hMutex->mutex), NULL))
	{
		int err = errno;
		free(hMutex);
		errno = err;
		hMutex = NULL;
	}
#endif
	return ((SYNCHANDLE)hMutex);
}

void ShareApi::MutexLock(SYNCHANDLE hMutex)
{
#ifdef WIN32
	::EnterCriticalSection(&(((PMTINFO)hMutex)->mutex));
#elif defined(LINUX)
	pthread_mutex_lock(&(((PMTINFO)hMutex)->mutex));
#endif
}

void ShareApi::MutexUnlock(SYNCHANDLE hMutex)
{
#ifdef WIN32
	::LeaveCriticalSection(&(((PMTINFO)hMutex)->mutex));
#elif defined(LINUX)
	pthread_mutex_unlock(&(((PMTINFO)hMutex)->mutex));
#endif
}

void ShareApi::CloseMutex(SYNCHANDLE hMutex)
{
#ifdef WIN32
	::DeleteCriticalSection(&(((PMTINFO)hMutex)->mutex));
#elif defined(LINUX)
	pthread_mutex_destroy(&(((PMTINFO)hMutex)->mutex));
#endif
	free((PMTINFO)hMutex);
}

SYNCHANDLE ShareApi::CreateCondVariable()
{
	PCONDVARINFO pCond = (PCONDVARINFO)malloc(sizeof(CONDVARINFO));
	if (pCond == NULL) return 0;
#ifdef WIN32
	InitializeConditionVariable(&pCond->cond);
#elif defined(LINUX)
	
	// 设置相对时间的属性
	if(pthread_condattr_init(&pCond->attr))
	{
		free(pCond);
		return 0;
	}
	else if(pthread_condattr_setclock(&pCond->attr, CLOCK_MONOTONIC))
	{
		pthread_condattr_destroy(&pCond->attr);
		free(pCond);
		return 0;
	}
	else
	{
		// 初始化条件变量
		if(pthread_cond_init(&pCond->cond, &pCond->attr))
		{
			// 失败
			pthread_condattr_destroy(&pCond->attr);
			free(pCond);
			return 0;
		}
	}
#endif

	return (SYNCHANDLE)pCond;
}

DWORD ShareApi::WaitConditionVariable(SYNCHANDLE hCond, SYNCHANDLE hMutex, DWORD dwTime)
{
	if(!hCond || !hMutex) return WAIT_FAILED;

	DWORD dwCode = 0;
	PMTINFO mutex = (PMTINFO)hMutex;
	PCONDVARINFO cond = (PCONDVARINFO)hCond;
#ifdef WIN32
	dwCode = ::SleepConditionVariableCS(&cond->cond, &mutex->mutex, dwTime);
	if (dwCode == 0)
	{
		if (::GetLastError() == ERROR_TIMEOUT) dwCode = WAIT_TIMEOUT;
		else dwCode = WAIT_FAILED;
	}
#elif defined(LINUX)
	// 如果有信号则不等待
	int nRet = 0;
	// 等待事件有信号
	if (dwTime != INFINITE)
	{
		// 设置相对时间而不是根据系统时间来决议
		timespec timeInfo;
		clock_gettime(CLOCK_MONOTONIC, &timeInfo);
		timeInfo.tv_sec += dwTime / 1000;
		timeInfo.tv_nsec += (dwTime % 1000) * 1000 * 1000; // 毫秒转纳秒
		nRet = pthread_cond_timedwait(&(cond->cond), &(mutex->mutex), &timeInfo);
	}
	else
	{
		// 没有超时等待,函数会一直等待事件有信号
		nRet = pthread_cond_wait(&(cond->cond), &(mutex->mutex));
	}

	// 判断返回值
	if (nRet == 0) {
		dwCode = WAIT_OBJECT_0;
	}else if (nRet == ETIMEDOUT) {
		dwCode = WAIT_TIMEOUT;
	}else {
		dwCode = WAIT_FAILED;
	}
#endif
	return dwCode;
}

BOOL ShareApi::NotifyAllCondVariable(SYNCHANDLE hCond)
{
	if (!hCond) return FALSE;

	BOOL bRet = TRUE;
	PCONDVARINFO cond = (PCONDVARINFO)hCond;
#ifdef WIN32
	WakeAllConditionVariable(&cond->cond);
#elif defined(LINUX)
	if (pthread_cond_broadcast(&cond->cond) != 0){
		bRet = FALSE;
	}
#endif

	return bRet;
}

BOOL ShareApi::NotifyCondVariable(SYNCHANDLE hCond)
{
	if (!hCond) return FALSE;

	BOOL bRet = TRUE;
	PCONDVARINFO cond = (PCONDVARINFO)hCond;
#ifdef WIN32
	WakeConditionVariable(&cond->cond);
#elif defined(LINUX)
	if (pthread_cond_signal(&cond->cond) != 0){
		bRet = FALSE;
	}
#endif // WIN32
	return bRet;
}

void ShareApi::CloseConditionVariable(SYNCHANDLE hCond)
{
	if (!hCond) return;
#ifdef WIN32
	free((PCONDVARINFO)hCond);
#elif defined(LINUX)
	PCONDVARINFO cond = (PCONDVARINFO)hCond;
	if (cond)
	{
		pthread_cond_destroy(&cond->cond);
		pthread_condattr_destroy(&cond->attr);
		free((PCONDVARINFO)cond);
	}
#endif // WIN32
}

#ifdef LINUX
// 检查所有事件是否都有信号
bool CheckAllEventHaveSignal(PSGINFO pSginfos, int nSignalCount)
{
	while(--nSignalCount >= 0)
	{
		if(!pSginfos[nSignalCount].IsEvent())
		{
			return false;
		}
	}
	return true;
}
// 检查是否有事件信号的存在
int CheckEventHaveSignal(PSGINFO pSginfos, int nSignalCount)
{
	while(--nSignalCount >= 0)
	{
		if(pSginfos[nSignalCount].IsEvent())
		{
			return nSignalCount;
		}
	}
	return -1;
}
#endif

long ShareApi::InterlockedExchangeAdd(long *Addend, long value)
{
#ifdef WIN32
	return ::_InterlockedExchangeAdd((LONG*)Addend, value);
#elif defined(LINUX)
	return __sync_fetch_and_add(Addend, value);
#endif
}

int ShareApi::InterlockedExchangeAdd(int *Addend, int value)
{
#ifdef WIN32
	return (int)::_InterlockedExchangeAdd((LONG*)Addend, value);
#elif defined(LINUX)
	return __sync_fetch_and_add(Addend, value);
#endif
}

INT64 ShareApi::InterlockedExchangeAdd64(INT64 *Addend, INT64 value)
{
#ifdef WIN32
	return ::InterlockedExchangeAdd64(Addend, value);
#elif defined(LINUX)
	return __sync_fetch_and_add(Addend, value);
#endif
}

long ShareApi::InterlockedExchange(long *Addend, long value)
{
#ifdef WIN32
	return ::_InterlockedExchange(Addend, value);
#elif defined(LINUX)
	return __sync_lock_test_and_set(Addend, value);
#endif
}

int ShareApi::InterlockedExchange(int *Addend, int value)
{
#ifdef WIN32
	return (int)::_InterlockedExchange((long*)Addend, value);
#elif defined(LINUX)
	return __sync_lock_test_and_set(Addend, value);
#endif
}

INT64 ShareApi::InterlockedExchange64(INT64* Addend, INT64 value)
{
#ifdef WIN32
	return ::InterlockedExchange64(Addend, value);
#elif defined(LINUX)
	return __sync_lock_test_and_set(Addend, value);
#endif
}

INT64 ShareApi::InterlockedIncrement64(INT64 *Addend)
{
	return ShareApi::InterlockedExchangeAdd64(Addend, 1);
}

long ShareApi::InterlockedIncrement(long *Addend)
{
	return ShareApi::InterlockedExchangeAdd(Addend, 1);
}

int ShareApi::InterlockedIncrement(int *Addend)
{
	return ShareApi::InterlockedExchangeAdd(Addend, 1);
}

INT64 ShareApi::InterlockedDecrement64(INT64 *Addend)
{
	return ShareApi::InterlockedExchangeAdd64(Addend, -1);
}

long ShareApi::InterlockedDecrement(long *Addend)
{
	return ShareApi::InterlockedExchangeAdd(Addend, -1);
}

int ShareApi::InterlockedDecrement(int *Addend)
{
	return ShareApi::InterlockedExchangeAdd(Addend, -1);
}

/********************** 同步相关(end) *************************/


/************************ 时间相关(start) ***********************/

// 线程休眠指定的时间
void ShareApi::Sleep(DWORD dwSleep)
{
#ifdef WIN32
	::Sleep(dwSleep);
#elif defined(LINUX)
	useconds_t udSleep = (useconds_t)dwSleep * 1000;
	usleep(udSleep);
#endif
}

// 获取从系统启动锁经过的时间(单位毫秒)
DWORD64 ShareApi::GetTickCount64()
{
#ifdef WIN32
	return ::GetTickCount64();
#elif defined(LINUX)
	struct timespec tMonTime = {0, 0};
	clock_gettime(CLOCK_MONOTONIC, &tMonTime);
	return (tMonTime.tv_sec * 1000 + tMonTime.tv_nsec / 1000000);
#endif
}

DWORD ShareApi::GetTickCount()
{
#ifdef WIN32
	return ::GetTickCount();
#elif defined(LINUX)
	struct timespec tMonTime = { 0, 0 };
	clock_gettime(CLOCK_MONOTONIC, &tMonTime);
	return (tMonTime.tv_sec * 1000 + tMonTime.tv_nsec / 1000000);
#endif
}

BOOL ShareApi::QueryPerformanceFrequency(LARGE_INTEGER *lpFrequency)
{
#ifdef WIN32
	return ::QueryPerformanceFrequency(lpFrequency);
#elif defined(LINUX)
	lpFrequency->QuadPart = 1;
	return TRUE;
#endif
}

BOOL ShareApi::QueryPerformanceCounter(LARGE_INTEGER *lpPerformanceCount)
{
#ifdef WIN32
	return ::QueryPerformanceCounter(lpPerformanceCount);
#elif defined(LINUX)
	struct timespec tMonTime = {0, 0};
	if(clock_gettime(CLOCK_MONOTONIC, &tMonTime) == 0)
	{
		lpPerformanceCount->QuadPart = tMonTime.tv_sec*1000000000 + tMonTime.tv_nsec;
		return TRUE;
	}
	return FALSE;
#endif
}
/************************ 时间相关(end)	  ***********************/


#ifdef WIN32
static bool g_bPrintConsole = false;
/**
 * @brief 打印函数调用堆栈
 * @remark 欲访问更多资料参考:
 *  https://stackoverflow.com/questions/22467604/how-can-you-use-capturestackbacktrace-to-capture-the-exception-stack-not-the-ca
 *	https://docs.microsoft.com/en-us/windows/desktop/api/dbghelp/
*/
void printStack(CONTEXT* ctx) //Prints stack trace based on context record
{
	BOOL    result;
	UTUTIL_HANDLE  process;
	UTUTIL_HANDLE  thread;
	HMODULE hModule;

	STACKFRAME64        stack;
	ULONG               frame;
	DWORD64             displacement;

	DWORD disp;
	IMAGEHLP_LINE64 *line;
	const int MaxNameLen = 256;

	char buffer[sizeof(SYMBOL_INFO) + MAX_SYM_NAME * sizeof(TCHAR)];
	char module[MaxNameLen];
	PSYMBOL_INFO pSymbol = (PSYMBOL_INFO)buffer;

	memset(&stack, 0, sizeof(STACKFRAME64));

	process = GetCurrentProcess();
	thread = GetCurrentThread();
	displacement = 0;
#if !defined(_M_AMD64)
	stack.AddrPC.Offset = (*ctx).Eip;
	stack.AddrPC.Mode = AddrModeFlat;
	stack.AddrStack.Offset = (*ctx).Esp;
	stack.AddrStack.Mode = AddrModeFlat;
	stack.AddrFrame.Offset = (*ctx).Ebp;
	stack.AddrFrame.Mode = AddrModeFlat;
#endif

	SymInitialize(process, NULL, TRUE); //load symbols

	for (frame = 0; ; frame++)
	{
		//get next call from stack
		result = StackWalk64
		(
#if defined(_M_AMD64)
			IMAGE_FILE_MACHINE_AMD64
#else
			IMAGE_FILE_MACHINE_I386
#endif
			,
			process,
			thread,
			&stack,
			ctx,
			NULL,
			SymFunctionTableAccess64,
			SymGetModuleBase64,
			NULL
		);

		if (!result) break;

		//get symbol name for address
		pSymbol->SizeOfStruct = sizeof(SYMBOL_INFO);
		pSymbol->MaxNameLen = MAX_SYM_NAME;
		SymFromAddr(process, (ULONG64)stack.AddrPC.Offset, &displacement, pSymbol);

		line = (IMAGEHLP_LINE64 *)malloc(sizeof(IMAGEHLP_LINE64));
		line->SizeOfStruct = sizeof(IMAGEHLP_LINE64);

		//try to get line
		if (SymGetLineFromAddr64(process, stack.AddrPC.Offset, &disp, line))
		{
			printf("\tat %s in %s: line: %lu: address: 0x%llx\n", pSymbol->Name, line->FileName, line->LineNumber, pSymbol->Address);
		}
		else
		{
			//failed to get line
			printf("\tat %s, address 0x%llx.\n", pSymbol->Name, pSymbol->Address);
			hModule = NULL;
			lstrcpyA(module, "");
			GetModuleHandleEx(GET_MODULE_HANDLE_EX_FLAG_FROM_ADDRESS | GET_MODULE_HANDLE_EX_FLAG_UNCHANGED_REFCOUNT,
				(LPCTSTR)(stack.AddrPC.Offset), &hModule);

			//at least print module name
			if (hModule != NULL)GetModuleFileNameA(hModule, module, MaxNameLen);

			printf("in %s\n", module);
		}

		free(line);
		line = NULL;
	}
}

LONG WINAPI TOOLS_TOP_LEVEL_EXCEPTION_FILTER(
	_In_ struct _EXCEPTION_POINTERS *ExceptionInfo
)
{
	if (g_bPrintConsole == true) {
		printStack(ExceptionInfo->ContextRecord);
	}

	TCHAR modulePath[MAX_PATH];
	HMODULE module = GetModuleHandle(NULL);
	if (module)
	{
		GetModuleFileName(module, modulePath, sizeof(modulePath));
		TCHAR* ptr = _tcsrchr(modulePath, '\\');
		if (ptr) *ptr = '\0';
	}

	tstring fileName;
	fileName = format_s(1024, TEXT("%s\\%s.dmp"), modulePath, TO_TSTRING((CTimeTools::GetDateStr())).c_str());

	FILE_HANDLE hFile = ShareApi::OpenFile(fileName.c_str(), FILE_TRUC | FILE_CREATEPLUS | FILE_READWRITE);
	if (hFile)
	{
		MINIDUMP_EXCEPTION_INFORMATION	M;
		M.ThreadId = GetCurrentThreadId();
		M.ExceptionPointers = ExceptionInfo;
		M.ClientPointers = FALSE;

		// 写入dump文件
		MiniDumpWriteDump(GetCurrentProcess(), GetCurrentProcessId(), (UTUTIL_HANDLE)hFile, MiniDumpNormal, &M, NULL, NULL);

		ShareApi::CloseFile(hFile);
	}

	return EXCEPTION_CONTINUE_SEARCH;
}
#elif defined(LINUX)

void printStack()
{
	const int BT_BUFF_SIZE = 100;
	void* buffer[BT_BUFF_SIZE];
	char** strings;

	int btSize = backtrace(buffer, BT_BUFF_SIZE);
	strings = backtrace_symbols(buffer, btSize);

	for (int i = 0; i < btSize; i++){
		printf("[%d] %s\n", i, strings[i]);
	}

	free(strings);
}

void deal_signal(int s, siginfo_t *si, void *unused)
{
	struct sigaction act;
	memset(&act, 0, sizeof(act));
	act.sa_flags = SA_SIGINFO;
	act.sa_handler = SIG_DFL;
	sigemptyset(&act.sa_mask);
	sigaction(s, &act, NullPtr);
	switch (s)
	{
	case SIGABRT:
		printf("signal SIGABRT\n");
		printStack();
		abort();
		break;
	case SIGSEGV:
		printf("signal SIGSEGV\n");
		printStack();
		exit(0);
		break;
	}
}

#endif // WIN32

void ShareApi::PrintDumpInfo(bool bPrintConsole /* = false */)
{
#ifdef WIN32
	g_bPrintConsole = bPrintConsole;
	SetUnhandledExceptionFilter(TOOLS_TOP_LEVEL_EXCEPTION_FILTER);
#else
	if (bPrintConsole)
	{
		struct sigaction act;
		act.sa_flags = SA_SIGINFO;
		act.sa_sigaction = deal_signal;
		sigemptyset(&act.sa_mask);
		sigaction(SIGABRT, &act, NULL);
		sigaction(SIGSEGV, &act, NULL);
	}
#endif // WIN32
}

BOOL ShareApi::StartProcess(const TCHAR* exename, TCHAR** argv /* = NullPtr */, TCHAR** evp /* = NullPtr */)
{
	BOOL ret = FALSE;
#ifdef WIN32
	tstring commandLine, evpLine;
	if (argv != NullPtr)
	{
		while (*argv != NullPtr)
		{
			commandLine += TEXT("\"");
			commandLine += *argv;
			commandLine += TEXT("\" ");
			++argv;
		}
	}
	else{
		commandLine = GetCommandLine();
	}
	char *evpParam = NullPtr;
	if (evp != NullPtr)
	{
		while (*evp != NullPtr)
		{
			evpLine += *evp;
			evpLine += TEXT('\0');
			++evp;
		}
		evpParam = (char*)evpLine.data();
	}
	TCHAR filePath[MAX_PATH] = { 0 };
	TCHAR *fileName = NullPtr;
	GetFullPathName(exename, MAX_PATH, filePath, &fileName);
	if (fileName) *fileName = TEXT('\0');
	STARTUPINFO si = { sizeof(si) };
	PROCESS_INFORMATION pi;
	si.dwFlags = STARTF_USESHOWWINDOW;
	si.wShowWindow = TRUE;
	ret = ::CreateProcess(exename, (LPTSTR)commandLine.data(), NullPtr, NullPtr, TRUE, CREATE_NEW_CONSOLE, evpParam, filePath, &si, &pi);
	if(ret == TRUE)
	{
		CloseHandle( pi.hProcess );
		CloseHandle( pi.hThread );
	}
#elif defined(LINUX)
	pid_t pid = fork();
	if (pid == 0)
	{
		int error = 0;
		// 子进程
		if (execve(exename, argv, evp) == -1){
			error = errno;
		}
		exit(error);
	}
	else if(pid != -1)
	{
		// 父进程
		int status = 0;
		waitpid(pid, &status, WUNTRACED | WCONTINUED);
		status = WEXITSTATUS(status);
		if(status != 0)
		{
			errno = status;
			ret = FALSE;
		}else{
			ret = TRUE;
		}
	}
#endif

	return ret;
}

UTUTIL_HANDLE ShareApi::LoadDll(const TCHAR* dllName)
{
	UTUTIL_HANDLE h = NullPtr;
#if WIN32
	h = ::LoadLibrary(dllName);
#elif defined(LINUX)
	h = ::dlopen(dllName, RTLD_LAZY);
	if (h == NullPtr)
	{
		fprintf(stderr, "%s\n", dlerror());
		errno = ENOENT;
	}
#endif
	return h;
}

void* ShareApi::LoadSymbol(UTUTIL_HANDLE dlHandle, const TCHAR* symbol)
{
	void* symb = NullPtr;
#ifdef WIN32
	symb = (void*)::GetProcAddress((HMODULE)dlHandle, TO_STRING(symbol).c_str());
#elif defined(LINUX)
	symb = dlsym(dlHandle, symbol);
	if (symb == NullPtr)
	{
		fprintf(stderr, "%s\n", dlerror());
		errno = ENOENT;
	}
#endif
	return symb;
}

void ShareApi::CloseDll(UTUTIL_HANDLE dlHandle)
{
	if (!dlHandle) return;
#ifdef WIN32
	FreeLibrary((HMODULE)dlHandle);
#elif defined(LINUX)
	dlclose(dlHandle);
#endif
}

DEF_END_UTUTIL
