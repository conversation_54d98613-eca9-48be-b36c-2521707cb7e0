﻿#ifndef __UTUTIL_RECURSIVE_MUTEX_H__
#define __UTUTIL_RECURSIVE_MUTEX_H__
#include "ututil_tools.h"
#include "ututil_mutex.h"

DEF_BEG_UTUTIL

/** 
 * @brief 递归锁
 * @remarks 用于递归函数中防止锁的重复调用造成的死锁
*/
class UTUTIL CRecursiveMutex:public CMutexBase
{
	delete_class_copy_operator(CRecursiveMutex);
public:
	CRecursiveMutex();
	virtual ~CRecursiveMutex();
	virtual void Lock();
	virtual void Unlock();
	virtual SYNCHANDLE Handle();
protected:
	bool Test_Recursive(long dwThread);
private:
	CMutex _mutex;
	long _lockThreadId;
	long _lockCount;
};

DEF_END_UTUTIL

#endif

