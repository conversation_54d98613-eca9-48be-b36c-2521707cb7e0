﻿#include "ututil_shareapi.h"
#include "ututil_hex.h"

USE_UTUTIL;
 
std::string CHexUtil::hexEncode(const unsigned char* data, size_t size) 
{  
	std::ostringstream oss;  
	oss << std::hex << std::setfill('0');  
	for (size_t i = 0; i < size; ++i) {  
		oss << std::setw(2) << static_cast<int>(data[i]);  
	}  
	return oss.str();  
}

std::vector<unsigned char> CHexUtil::hexDecode(const std::string& hexStr) 
{
	//偶数位
	std::string newHexStr;
	if(0 !=hexStr.size()%2)
	{
		newHexStr ="0"+ hexStr;
	}
	else
	{
		newHexStr = hexStr;
    }
	std::vector<unsigned char> data;
	while(newHexStr.size() >= 2)
    {
        std::string tmpStr = newHexStr.substr(0, 2);
		std::istringstream iss(tmpStr);    
		unsigned int value;  
		iss >> std::hex >> value;		
		data.push_back(value);  		
        newHexStr = newHexStr.substr(2, newHexStr.size()-2);
	}
	return data;  
}

