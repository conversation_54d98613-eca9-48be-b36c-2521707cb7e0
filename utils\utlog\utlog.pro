QT     -= gui core
CONFIG += c++11
CONFIG += plugin debug

TARGET = utlog
TEMPLATE    = lib
DESTDIR     = $$(UTSTENV)/shlib

OBJECTS_DIR = $$(UTSTENV)/object/utils/utlog
MOC_DIR = $$(UTSTENV)/moc/utils/utlog

QMAKE_CXXFLAGS += -std=c++11
QMAKE_CXXFLAGS += -fPIC -pthread

DEFINES += UTLOG_LIBRARY


SOURCES += $$(UTSTENV)/utils/utlog/src/utlog.cpp

HEADERS += $$(UTSTENV)/utils/utlog/src/utlogger.h \
        $$(UTSTENV)/utils/utlog/include/utlog.h


INCLUDEPATH += \
        $$(UTSTENV)/utils/utlog/include \
        $$(UTSTENV)/utils/ututil/include \
        $$(UTSTENV)/utils/utlog/third \
        $$(UTSTENV)/utils/utlog/third/spdlog

LIBS += -L$$(UTSTENV)/shlib \
        -lututil
