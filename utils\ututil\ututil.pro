QT -= gui
QT += core
CONFIG += c++11
CONFIG += plugin debug


TARGET = ututil
TEMPLATE = lib
DESTDIR = $$(UTSTENV)/shlib

OBJECTS_DIR = $$(UTSTENV)/object/utils/ututil
MOC_DIR = $$(UTSTENV)/moc/utils/ututil

QMAKE_CXXFLAGS += -std=c++11 -DSTD11 -std=gnu++11
QMAKE_CXXFLAGS += -fPIC -pthread -shared -rdynamic -fpermissive

DEFINES += UTUTIL_EXPORT
        

SOURCES += \
    $$(UTSTENV)/utils/ututil/src/ututil_autolock.cpp \
    $$(UTSTENV)/utils/ututil/src/ututil_ini.cpp \
    $$(UTSTENV)/utils/ututil/src/ututil_elapsed_timer.cpp \
    $$(UTSTENV)/utils/ututil/src/ututil_file.cpp \
    $$(UTSTENV)/utils/ututil/src/ututil_json.cpp \
    $$(UTSTENV)/utils/ututil/src/ututil_mutex.cpp \
    $$(UTSTENV)/utils/ututil/src/ututil_recursive_mutex.cpp \
    $$(UTSTENV)/utils/ututil/src/ututil_shareapi.cpp \
    $$(UTSTENV)/utils/ututil/src/ututil_string.cpp \
    $$(UTSTENV)/utils/ututil/src/ututil_thread.cpp \
    $$(UTSTENV)/utils/ututil/src/ututil_time.cpp \
    $$(UTSTENV)/utils/ututil/src/ututil_md5.cpp \
    $$(UTSTENV)/utils/ututil/src/ututil_task.cpp \
    $$(UTSTENV)/utils/ututil/src/ututil_random.cpp \
    $$(UTSTENV)/utils/ututil/src/ututil_net.cpp \
    $$(UTSTENV)/utils/ututil/src/ututil_watchdog.cpp \
    $$(UTSTENV)/utils/ututil/src/ututil_hex.cpp \
    $$(UTSTENV)/utils/ututil/third/iniparser/dictionary.c \
    $$(UTSTENV)/utils/ututil/third/iniparser/iniparser.c \
    $$(UTSTENV)/utils/ututil/third/jsoncpp/jsoncpp.cpp \
    $$(UTSTENV)/utils/ututil/third/simpleini/convertutf.c


HEADERS += \
     $$(UTSTENV)/utils/ututil/include/__ututil_tools.h \
     $$(UTSTENV)/utils/ututil/include/ututil_algorithm.h \
     $$(UTSTENV)/utils/ututil/include/ututil_autolock.h \
     $$(UTSTENV)/utils/ututil/include/ututil_counter.h \
     $$(UTSTENV)/utils/ututil/include/ututil_ini.h \
     $$(UTSTENV)/utils/ututil/include/ututil_elapsed_timer.h \
     $$(UTSTENV)/utils/ututil/include/ututil_event.h \
     $$(UTSTENV)/utils/ututil/include/ututil_file.h \
     $$(UTSTENV)/utils/ututil/include/ututil_functional.h \
     $$(UTSTENV)/utils/ututil/include/ututil_json.h \
     $$(UTSTENV)/utils/ututil/include/ututil_mutex.h \
     $$(UTSTENV)/utils/ututil/include/ututil_recursive_mutex.h \
     $$(UTSTENV)/utils/ututil/include/ututil_safelist.h \
     $$(UTSTENV)/utils/ututil/include/ututil_safemap.h \
     $$(UTSTENV)/utils/ututil/include/ututil_safeset.h \
     $$(UTSTENV)/utils/ututil/include/ututil_shareapi.h \
     $$(UTSTENV)/utils/ututil/include/ututil_sharedptr.h \
     $$(UTSTENV)/utils/ututil/include/ututil_single_factory.h \
     $$(UTSTENV)/utils/ututil/include/ututil_string.h \
     $$(UTSTENV)/utils/ututil/include/ututil_thread.h \
     $$(UTSTENV)/utils/ututil/include/ututil_time.h \
     $$(UTSTENV)/utils/ututil/include/ututil_tools.h \
     $$(UTSTENV)/utils/ututil/include/ututil_warning.h \
     $$(UTSTENV)/utils/ututil/include/ututil_md5.h \
     $$(UTSTENV)/utils/ututil/include/ututil_task.h \
     $$(UTSTENV)/utils/ututil/include/ututil_random.h \
     $$(UTSTENV)/utils/ututil/include/ututil_watchdog.h \
     $$(UTSTENV)/utils/ututil/include/ututil_hex.h

INCLUDEPATH +=  $${THIRD_INC_PATH} \
         $$(UTSTENV)/utils/ututil/include \
        $$(UTSTENV)/utils/ututil/third/
