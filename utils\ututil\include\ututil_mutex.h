﻿#ifndef __UTUTIL_MUTEX_H__
#define __UTUTIL_MUTEX_H__

#include "ututil_shareapi.h"

DEF_BEG_UTUTIL

/**
 * @brief 锁基类
 */
class UTUTIL CMutexBase
{
	delete_class_copy_operator(CMutexBase);
public:
	CMutexBase() {}
	virtual ~CMutexBase() {}
	virtual void Lock() = 0;
	virtual void Unlock() = 0;
	virtual SYNCHANDLE Handle() = 0;
};

/**
 * @brief 常规锁 
 */ 
class UTUTIL CMutex:public CMutexBase
{
    delete_class_copy_operator(CMutex);
public:
    CMutex();
    virtual ~CMutex();
	virtual void Lock();
	virtual void Unlock();
	virtual SYNCHANDLE Handle();
private:
	SYNCHANDLE _mutex;
};

/**
 * @brief 常规锁包装器
 */ 
class UTUTIL CLockGurad
{
	delete_class_copy_operator(CLockGurad);
public:
	CLockGurad(CMutexBase* mutex);
	~CLockGurad();
	void Lock();
	void Unlock();
private:
	CMutexBase* _mutex;
};

DEF_END_UTUTIL

#endif
