﻿#ifndef __UTUTIL_FILE_H__
#define __UTUTIL_FILE_H__

#include "ututil_tools.h"
#include "ututil_shareapi.h"

#include <sys/stat.h>
#include <sys/types.h>
#include <iostream>
#include <fstream>
#include <string.h>

#ifdef WIN32
#include <direct.h>
#include <io.h>
typedef unsigned short mode_t;
#define S_IFREG _S_IFREG		//表示为普通文件，为了跨平台，一律使用S_IFREG
#define S_IFDIR _S_IFDIR			//表示为目录，为了跨平台，一律使用S_IFDIR
#else
#include <unistd.h>
#include <strings.h>
#include <sys/stat.h>
#include <sys/dir.h>
#include <sys/types.h>
#endif

#include <stdio.h>
#include <string>
#include <stdint.h>
#include <vector>
#include <stack>

DEF_BEG_UTUTIL

using namespace std;

#ifdef WIN32
#define FILE_SEP "\\"
#else
#define FILE_SEP "/"
#endif

#ifdef WIN32
    typedef struct _stat stat_t;
#else
    typedef struct stat stat_t;
#endif

typedef UINT64 bigint;


/**
 * @brief 文件操作基类
 * @brief 主要解决大文件打开问题
 */
class UTUTIL CFileObject
{
public:
	CFileObject() {}
	virtual bool Open(const TCHAR* sPath,unsigned int mode) = 0;
	virtual void Close() = 0;
	virtual const TCHAR* GetLastError() const = 0;
	virtual ~CFileObject() {};
};

/**
 * @brief 文件操作类
 */
class UTUTIL CFile : public CFileObject
{
public:
	/**
	 * @brief 文件位置枚举
	 */
	enum PosCode
	{
		beg = 0,	///< 文件开始处
		cur = 1,	///< 当前文件指针指向的位置
		end = 2		///< 文件结束位置
	};

	/**
	 * @brief 文件打开模式枚举
	 */
	enum ModeCode
	{
		OnlyRead = 1,			///< 只读
		OnlyWrite = 2,			///< 只写
		ReadWrite = 4,			///< 读写
		Share = 8,				///< 共享
		Independent = 16,		///< 独进程,linux不支持此标志
		Truc = 32,				///< 清空				
		Hide = 64,				///< 隐藏文件
		CreatePlus = 256		///< 如果文件不存在则创建此文件，存在则打开它
	};
public:
	// 初始化构造函数
	CFile();
	// 析构函数
	virtual ~CFile();
	/** 
	 * @brief 构造函数，打开文件
	 * @param sPath 文件名称
	 * @param mode 文件打开模式：
	 *	@see CFile::ModeCode
	*/
	CFile(const TCHAR* sPath,unsigned int mode);

	/** 
	 * @brief 打开文件
	 * @param sPath 文件名称
	 * @param mode 文件打开模式:
	 *	@see CFile::ModeCode
	*/
	virtual bool Open(const TCHAR* sPath, unsigned int mode);

	//! 关闭文件
	virtual void Close();

	//! 返回上一次错误的信息
	virtual const TCHAR* GetLastError() const;

	//! 判断文件是否打开
	bool IsOpen() const;

	/**
	 * @name 获取文件大小，单位字节,成功返回文件大小的字节数，失败返回-1
	 */
	bigint GetFileLengthEx() const;
	fsize_t GetFileLength() const;

	/**
	 * @brief 读取文件，
	 * @param buf 读取的缓冲区
	 * @param size 读取元素的个数
	 * @return 如果成功返回读取的字节数，失败则返回-1
	 */
	virtual fsize_t Read(void *buf, fsize_t size);

	/**
	 * @brief 写入数据到文件中
	 * @param buf 写入的缓冲区
	 * @param size 写入的元素个数
	 * @return 如果成功返回写入的字节数，失败则返回-1
	*/
	virtual fsize_t Write(const void *buf, fsize_t size);
	
	/**
	 * @brief 设置文件指针位置
	 * @param offset 偏移量位置
	 * @param origin 以何处为基准，参考 enum PosCode
	 * @return 成功返回true,失败返回false
	*/
	virtual bool Seek(bigint offset, unsigned int origin);

	//! 获取文件当前指针位置,失败返回-1，成功返回文件当前的指针位置
	virtual bigint CurPosEx() const;
	virtual fsize_t CurPos() const;

	//! 判断是否到达文件尾
	virtual bool Eof() const;

protected:
	bool _Open(const TCHAR* sPath, unsigned int mode);
	delete_class_copy_operator(CFile);

private:
	FILE_HANDLE _hfDes;
	TCHAR _errMsg[MAX_PATH];
};


class CFileUtil
{
public:

    /**
     * @brief 更改文件权限
     * @param path 文件路径
     * @param mode 权限
     * mode的值　　　　　存取权限
       S_IWRITE 　　　　　允许写
       S_IREAD　　　　　　允许读(只读)
       S_IREAD|S_IWRITE 　可读可写
     * @return 0(成功)；-1(失败)
     */
    static int chmod(const char *path, mode_t mode);

    /**
    * @brief 创建目录, 如果目录已经存在, 则也返回成功.
    *
    * @param sFullPath 要创建的目录名称
    * @return bool  true-创建成功 ，false-创建失败
    */
    static bool makeDir(const std::string &sDirectoryPath);

    /**
     *@brief 循环创建目录, 如果目录已经存在, 则也返回成功.
     *
     * @param sFullPath 要创建的目录名称
     * @return           true-创建成功，false-创建失败
     */

    static bool makeDirRecursive(const std::string &sDirectoryPath);


    /**
     * @brief 删除一个文件或目录.
     *
     * @param sFullFileName 文件或者目录的全路径
     * @param bRecursive    如果是目录是否递归删除
     * @return              0-成功，失败可以通过errno查看失败的原因
     */
    static int remove(const std::string &sFullFileName, bool bRecursive);

    /**
     * @brief 重命名一个文件或目录.
     *
     * @param sSrcFullFileName 源文件名
     * @param sDstFullFileName 目的文件名
     * @return              0-成功，失败可以通过errno查看失败的原因
     */
    static int rename(const std::string &sSrcFullFileName, const std::string &sDstFullFileName);


    /**
     * @brief 判断是否为绝对路径, 忽略空格以'/'开头.
     *
     * @param sFullFileName 文件全路径(所在目录和文件名)
     * @return              ture是绝对路径，false代表非绝对路径
     */
    static bool isAbsolute(const std::string &sFullFileName);

    /**
    * @brief 判断给定路径的目录或文件是否存在.
    * @param sFullName 给定全路径
    * @return  true代表存在，fals代表不存在
    */
    static bool isExist(const std::string &sFullName);

    /**
    * @brief 判断给定路径的目录是否存在.
    * @param sFullDirName 目录全路径
    * @return  true代表存在，fals代表不存在
    */
    static bool isDirExist(const std::string &sFullDirName);

    /**
    * @brief 判断给定路径的文件是否存在.
    * 注意: 如果文件是符号连接,则以符号连接判断而不是以符号连接指向的文件判断
    * @param sFullFileName 文件全路径
    * @param iFileType     文件类型, 缺省S_IFREG
    *   S_ISLNK(st_mode):是否是一个连接.
        S_ISREG是否是一个常规文件.
        S_ISDIR是否是一个目录
        S_ISCHR是否是一个字符设备
        S_ISBLK是否是一个块设备
        S_ISFIFO是否 是一个FIFO文件
        S_ISSOCK是否是一个SOCKET文件
    * @return  true代表存在，fals代表不存在
    */
    static bool isFileExist(const std::string &sFullFileName, mode_t iFileType = S_IFREG);

    /**
    * @brief 判断给定路径的文件是否存在.
    * 注意: 如果文件是符号连接,则以符号连接指向的文件判断
    * @param sFullFileName  文件全路径
    * @param iFileType      文件类型, 缺省S_IFREG
    *   S_ISLNK(st_mode):是否是一个连接.
        S_ISREG是否是一个常规文件.
        S_ISDIR是否是一个目录
        S_ISCHR是否是一个字符设备
        S_ISBLK是否是一个块设备
        S_ISFIFO是否 是一个FIFO文件
        S_ISSOCK是否是一个SOCKET文件
    * @return               true-存在，fals-不存在
    */
    static bool isFileExistEx(const std::string &sFullFileName, mode_t iFileType = S_IFREG);

    /**
    * @brief 写文件.
    *
    * @param sFullFileName 文件名称
    * @param sFileData     文件内容
    * @return
    */
    static void save2file(const std::string &sFullFileName, const std::string &sFileData);

    /**
     * @brief 写文件.
     *
     * @param sFullFileName  文件名
     * @param sFileData      数据指针
     * @param length      写入长度
     * @return               0-成功,-1-失败
     */
    static int save2file(const std::string &sFullFileName, const char *sFileData, size_t length);


    /**
    * @brief 获取文件大小, 如果文件不存在, 则返回-1.
    *
    * @param  sFullFileName 文件全路径(所在目录和文件名)
    * @return               ofstream::pos_type类型文件大小
    */
    static ifstream::pos_type getFileSize(const std::string &sFullFileName);

    /**
     * @brief 遍历目录, 获取目录下面的所有文件和子目录.
     *
     * @param path       需要遍历的路径
     * @param files      目标路径下面所有文件
     * @param bRecursive 是否递归子目录
     *
     **/
    static void listDirectory(const std::string &path, vector<std::string> &files, bool bRecursive);


    /**
    * @brief 复制文件或目录.
    * 将文件或者目录从sExistFile复制到sNewFile
    * @param sExistFile 复制的文件或者目录源路径
    * @param sNewFile   复制的文件或者目录目标路径
    * @param bRemove    是否先删除sNewFile再copy ，防止Textfile busy导致复制失败
    * @return
    */
    static void copyFile(const std::string &sExistFile, const std::string &sNewFile, bool bRemove = false);


    /**
    * @brief 提取文件名称
    *从一个完全文件名中去掉路径，例如:/usr/local/temp.gif获取temp.gif
    *@param sFullFileName  文件的完全名称
    *@return std::string        提取后的文件名称
    */
    static std::string extractFileName(const std::string &sFullFileName);

    /**
    * @brief 从一个完全文件名中提取文件的路径.
    *
    * 例如1: "/usr/local/temp.gif" 获取"/usr/local/"
    * 例如2: "temp.gif" 获取 "./"
    * @param sFullFileName 文件的完全名称
    * @return              提取后的文件路径
    */
    static std::string extractFilePath(const std::string &sFullFileName);

    /**
    * @brief 提取文件扩展名.
    *
    * 例如1: "/usr/local/temp.gif" 获取"gif"
    * 例如2: "temp.gif" 获取"gif"
    *@param sFullFileName 文件名称
    *@return              文件扩展名
    */
    static std::string extractFileExt(const std::string &sFullFileName);

    /**
    * @brief 提取文件名称,去掉扩展名.
    * 例如1: "/usr/local/temp.gif" 获取"/usr/local/temp"
    * 例如2: "temp.gif" 获取"temp"
    * @param sFullFileName 文件名称
    * @return              去掉扩展名的文件名称
    */
    static std::string excludeFileExt(const std::string &sFullFileName);

    /**
    * @brief 替换文件扩展名
    *
    * 改变文件类型，如果无扩展名,则加上扩展名 =?1:
    * 例如1："/usr/temp.gif" 替 换 "jpg" 得到"/usr/temp.jpg"
    * 例如2: "/usr/local/temp" 替 换 "jpg" 得到"/usr/local/temp.jpg"
    * @param sFullFileName 文件名称
    * @param sExt          扩展名
    * @return              替换扩展名后的文件名
    */
    static std::string replaceFileExt(const std::string &sFullFileName, const std::string &sExt);

    /**
    * @brief 从一个url中获取完全文件名.
    *
    * 获取除http://外,第一个'/'后面的所有字符
    * 例如1:http://www.qq.com/tmp/temp.gif 获取tmp/temp.gif
    * 例如2:www.qq.com/tmp/temp.gif 获取tmp/temp.gif
    * 例如3:/tmp/temp.gif 获取tmp/temp.gif
    * @param sUrl url字符串
    * @return     文件名称
    */
    static std::string extractUrlFilePath(const std::string &sUrl);

#ifdef LINUX
    /**
    * @brief 遍历文件时确定是否选择.
    *
    * @return 1-选择, 0-不选择
    */
    typedef int(*FILE_SELECT)(const dirent *);

    /**
    * @brief 扫描一个目录.
    *
    * @param sFilePath     需要扫描的路径
    * @param vtMatchFiles  返回的文件名矢量表
    * @param f             匹配函数,为NULL表示所有文件都获取
    * @param iMaxSize      最大文件个数,iMaxSize <=0时,返回所有匹配文件
    * @return              文件个数
    */
    static std::size_t scanDir(const std::string &sFilePath, std::vector<std::string> &vtMatchFiles, FILE_SELECT f = NULL, int iMaxSize = 0);
#endif


    /**
     * @brief 打开文件
     * @param path 文件路径
     * @param mode 打开模式
     *  “r”	    只能读
        “w”	    只能写，无此文件则创建，有此文件则清空
        “a”	    只能写，会在原来的文件后面添加内容。无此文件则创建文件
        “r+”	读写文件
        “w+”	读写文件，无此文件则创建，有此文件则清空
        “a+”	读写文件，会在原来的文件后面添加内容。无此文件则创建文件
     * @return 文件指针
     */
    static FILE * fopen(const char * path, const char * mode);

    /**
     * @brief 获取相关文件状态信息
     * @param path 文件路径
     * @param buf 文件状态信息
     * @return 执行成功则返回0，失败返回-1，错误代码存于errno
     */
    static int lstat(const char * path, stat_t * buf);

    /**
     * @brief 规则化目录名称, 把一些不用的去掉, 例如./等.
     *
     * @param path 目录名称
     * @return        规范后的目录名称
     */
    static std::string simplifyDirectory(const std::string& path);

    /**
     * @brief 路径是否以windows盘符开头，如:  c:/abc/data.dat
     * @param sPath 路径
     * @return true- 是，false-否
     */
    static bool startWindowsPanfu(const std::string & sPath);

    /**
     * @brief 是否是windows盘符，如:  c:
     * @param sPath 路径
     * @return true- 是，false-否
     */
    static bool isPanfu(const std::string &sPath);

private:
    /**
     * @brief 新建文件夹
     * @param path
     * @return
     */
    static int mkdir(const char *path);

    /**
     * @brief 移除文件夹
     * @param path
     * @return
     */
    static int rmdir(const char *path);

};

DEF_END_UTUTIL
#endif // FILEUTIL_H
