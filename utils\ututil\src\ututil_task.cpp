﻿#include <iostream>
#include <exception>
#include "ututil_task.h"
#include "ututil_thread.h"


USE_UTUTIL;

void CTaskPool::runInThread(void *param)
{
    CTaskPool *taskPool = (CTaskPool *) param;
    while (!taskPool->m_stop.load())
    {
        std::shared_ptr<CTask> task = taskPool->take();
        if(!task)
        {
            continue;
        }
        //std::string taskName = task->getTaskName();
        //std::cout << "Thread got task:" << taskName << std::endl;
        try
        {
            task->run();
        }
        catch (std::exception &e)
        {
            std::string errMsg = "Exception happens when executing:\n";
            errMsg += "Thread pool Name:" + taskPool->m_poolName + " Task name:"+ task->getTaskName() +"\n";
            errMsg += "Raison:"+ std::string(e.what()) +"\n";
            std::cout << errMsg;
        }
        catch (...)
        {
            std::string errMsg = "Unknown exception happens when executing:\n";
            errMsg += "Thread pool Name:" + taskPool->m_poolName + " Task name:"+ task->getTaskName() +"\n";
            std::cout << errMsg;
        }
    }
    taskPool->m_stopedCount++;//线程退出一个增加一个（原子操作）
    return;
}

CTaskPool::~CTaskPool()
{
    stop();

}

std::shared_ptr<CTask> CTaskPool::take()
{
    std::unique_lock<std::mutex> lock(m_mutex);
    m_notEmptyCv.wait(lock,
                      [this] {
                      return this->m_stop.load() || !this->m_taskList.empty();
                      });
    if (!m_taskList.empty())
    {
        std::shared_ptr<CTask> curTask = m_taskList.front();
        m_taskList.pop_front();
        m_notFullCv.notify_one();
        return curTask;
    }
    return NULL;
}

void CTaskPool::put(std::shared_ptr<CTask> task)
{
    std::unique_lock<std::mutex> lock(m_mutex);
    m_notFullCv.wait(lock,
                     [this]{
                     return this->m_stop.load() || m_taskList.size() < m_poolSize;
                     });
    m_taskList.push_back(task);
    m_notEmptyCv.notify_one();
}

bool CTaskPool::start(std::string &errMsg) {
    if (!m_stop.load())
    {
        errMsg = "ERROR: Thread pool named '"+m_poolName+"' is started multiple times!";
        return false;
    }
    if(m_poolSize <= 0)
    {
        errMsg = "ERROR: Can not start task pool! pool size must larger than 0!";
        return false;
    }
    m_stopedCount.store(0);
    m_stop.store(false);
    for (unsigned int i = 0; i < m_poolSize; i++)
    {
        std::thread currentThread = std::thread(runInThread, this);
        currentThread.detach();
    }
    return true;
}

void CTaskPool::stop()
{
    if (m_stop.load()) {
        return;
    }
    m_stop.store(true);
    do
    {
        std::unique_lock<std::mutex> lock(m_mutex);
        m_notEmptyCv.notify_all();
        m_notFullCv.notify_all();
    }while(0);

    //std::cout << "m_poolSize:" << m_poolSize << ", m_stopedCount:" << this->m_stopedCount.load() << std::endl;
    while(this->m_stopedCount.load() != m_poolSize) //等待全部任务线程完全退出
    {
        CThreadUtil::sleepMs(100);
        //std::cout << "wait..all stoped..m_stopedCount:" << this->m_stopedCount << std::endl;
    }

    //如果还有任务未完成，则执行完成后退出
    //std::cout << "m_taskList.size():" << m_taskList.size()  << std::endl;
    while(m_taskList.size() > 0)
    {
        std::shared_ptr<CTask> task = this->take();
        if(task)
        {
            //std::cout << "stop.... run" << std::endl;
            task->run();
        }
    }
}

